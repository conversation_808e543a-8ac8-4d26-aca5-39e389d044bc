#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多工作表选择对话框

提供支持复选框的多工作表选择功能，包括全选/取消全选等便捷操作。
"""

from typing import List, Dict, Any, Optional
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QCheckBox, QScrollArea, QWidget, QFrame, QGroupBox,
    QDialogButtonBox, QSizePolicy
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

from src.utils.log_config import setup_logger


class MultiSheetSelectionDialog(QDialog):
    """多工作表选择对话框
    
    支持复选框多选、全选/取消全选、预览工作表信息等功能
    """
    
    # 信号定义
    sheets_selected = pyqtSignal(list)  # 选中的工作表列表
    
    def __init__(self, parent=None, config_name: str = "", sheets_data: Dict[str, Any] = None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 基本属性
        self.config_name = config_name
        self.sheets_data = sheets_data or {}
        self.selected_sheets = []
        
        # UI组件
        self.sheet_checkboxes = {}
        self.select_all_checkbox = None
        self.info_labels = {}
        
        self._init_ui()
        self._setup_connections()
        self._load_sheet_data()
        
        self.logger.info(f"多工作表选择对话框初始化完成，配置: {config_name}, 工作表数: {len(self.sheets_data)}")
    
    def _init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(f"选择工作表 - {self.config_name}")
        self.setModal(True)
        self.resize(500, 400)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(12)
        
        # 标题和说明
        self._create_header(main_layout)
        
        # 全选控制区域
        self._create_select_all_section(main_layout)
        
        # 工作表列表区域
        self._create_sheets_section(main_layout)
        
        # 按钮区域
        self._create_buttons_section(main_layout)
        
        # 应用样式
        self._apply_styles()
    
    def _create_header(self, parent_layout):
        """创建标题和说明区域"""
        header_frame = QFrame()
        header_layout = QVBoxLayout(header_frame)
        
        # 主标题
        title_label = QLabel(f"配置 '{self.config_name}' 包含多个工作表")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label)
        
        # 说明文字
        desc_label = QLabel("请选择要加载的工作表（支持多选）：")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("color: #666; margin: 5px 0;")
        header_layout.addWidget(desc_label)
        
        parent_layout.addWidget(header_frame)
    
    def _create_select_all_section(self, parent_layout):
        """创建全选控制区域"""
        select_all_frame = QFrame()
        select_all_layout = QHBoxLayout(select_all_frame)
        select_all_layout.setContentsMargins(10, 5, 10, 5)
        
        # 全选复选框
        self.select_all_checkbox = QCheckBox("全选")
        self.select_all_checkbox.setStyleSheet("font-weight: bold; color: #2196F3;")
        select_all_layout.addWidget(self.select_all_checkbox)
        
        select_all_layout.addStretch()
        
        # 快捷按钮
        select_all_btn = QPushButton("全选")
        select_all_btn.setMaximumWidth(80)
        select_all_btn.clicked.connect(self._select_all_sheets)
        select_all_layout.addWidget(select_all_btn)
        
        deselect_all_btn = QPushButton("取消全选")
        deselect_all_btn.setMaximumWidth(80)
        deselect_all_btn.clicked.connect(self._deselect_all_sheets)
        select_all_layout.addWidget(deselect_all_btn)
        
        parent_layout.addWidget(select_all_frame)
    
    def _create_sheets_section(self, parent_layout):
        """创建工作表列表区域"""
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # 滚动内容容器
        scroll_widget = QWidget()
        self.sheets_layout = QVBoxLayout(scroll_widget)
        self.sheets_layout.setSpacing(8)
        
        scroll_area.setWidget(scroll_widget)
        parent_layout.addWidget(scroll_area)
    
    def _create_buttons_section(self, parent_layout):
        """创建按钮区域"""
        button_box = QDialogButtonBox()
        
        # 确定按钮
        ok_button = button_box.addButton("确定", QDialogButtonBox.AcceptRole)
        ok_button.setDefault(True)
        ok_button.clicked.connect(self._on_accept)
        
        # 取消按钮
        cancel_button = button_box.addButton("取消", QDialogButtonBox.RejectRole)
        cancel_button.clicked.connect(self.reject)
        
        parent_layout.addWidget(button_box)
    
    def _setup_connections(self):
        """设置信号连接"""
        if self.select_all_checkbox:
            self.select_all_checkbox.toggled.connect(self._on_select_all_toggled)
    
    def _load_sheet_data(self):
        """加载工作表数据"""
        if not self.sheets_data:
            self.logger.warning("没有工作表数据可加载")
            return
        
        for sheet_name, sheet_config in self.sheets_data.items():
            self._create_sheet_item(sheet_name, sheet_config)
        
        # 添加弹性空间
        self.sheets_layout.addStretch()
        
        self.logger.info(f"已加载 {len(self.sheets_data)} 个工作表选项")
    
    def _create_sheet_item(self, sheet_name: str, sheet_config: Dict[str, Any]):
        """创建单个工作表选项"""
        # 创建工作表组框
        sheet_group = QGroupBox()
        sheet_layout = QVBoxLayout(sheet_group)
        sheet_layout.setSpacing(4)
        
        # 工作表复选框
        checkbox = QCheckBox(sheet_name)
        checkbox.setObjectName(sheet_name)
        checkbox.toggled.connect(self._on_sheet_toggled)
        self.sheet_checkboxes[sheet_name] = checkbox
        sheet_layout.addWidget(checkbox)
        
        # 工作表信息
        info_text = self._format_sheet_info(sheet_config)
        if info_text:
            info_label = QLabel(info_text)
            info_label.setStyleSheet("color: #666; font-size: 11px; margin-left: 20px;")
            info_label.setWordWrap(True)
            self.info_labels[sheet_name] = info_label
            sheet_layout.addWidget(info_label)
        
        self.sheets_layout.addWidget(sheet_group)
    
    def _format_sheet_info(self, sheet_config: Dict[str, Any]) -> str:
        """格式化工作表信息"""
        info_parts = []
        
        # 字段数量
        config_data = sheet_config.get('config', {})
        if config_data:
            field_count = len(config_data)
            info_parts.append(f"字段数: {field_count}")
        
        # 描述信息
        description = sheet_config.get('description', '')
        if description:
            info_parts.append(f"描述: {description}")
        
        # 更新时间
        updated_at = sheet_config.get('updated_at', '')
        if updated_at:
            info_parts.append(f"更新: {updated_at[:19]}")  # 只显示日期时间部分
        
        return " | ".join(info_parts)
    
    def _on_select_all_toggled(self, checked: bool):
        """处理全选复选框切换"""
        # 临时断开信号连接，避免循环触发
        for checkbox in self.sheet_checkboxes.values():
            checkbox.toggled.disconnect(self._on_sheet_toggled)
            checkbox.setChecked(checked)
            checkbox.toggled.connect(self._on_sheet_toggled)
    
    def _on_sheet_toggled(self):
        """处理单个工作表复选框切换"""
        # 更新全选复选框状态
        checked_count = sum(1 for cb in self.sheet_checkboxes.values() if cb.isChecked())
        total_count = len(self.sheet_checkboxes)
        
        if checked_count == 0:
            self.select_all_checkbox.setCheckState(Qt.Unchecked)
        elif checked_count == total_count:
            self.select_all_checkbox.setCheckState(Qt.Checked)
        else:
            self.select_all_checkbox.setCheckState(Qt.PartiallyChecked)
    
    def _select_all_sheets(self):
        """全选所有工作表"""
        # 临时断开信号连接，避免循环触发
        for checkbox in self.sheet_checkboxes.values():
            checkbox.toggled.disconnect(self._on_sheet_toggled)
            checkbox.setChecked(True)
            checkbox.toggled.connect(self._on_sheet_toggled)
        # 更新全选复选框状态
        self.select_all_checkbox.setChecked(True)

    def _deselect_all_sheets(self):
        """取消选择所有工作表"""
        # 临时断开信号连接，避免循环触发
        for checkbox in self.sheet_checkboxes.values():
            checkbox.toggled.disconnect(self._on_sheet_toggled)
            checkbox.setChecked(False)
            checkbox.toggled.connect(self._on_sheet_toggled)
        # 更新全选复选框状态
        self.select_all_checkbox.setChecked(False)
    
    def _on_accept(self):
        """处理确定按钮点击"""
        # 收集选中的工作表
        self.selected_sheets = [
            sheet_name for sheet_name, checkbox in self.sheet_checkboxes.items()
            if checkbox.isChecked()
        ]
        
        if not self.selected_sheets:
            # 没有选择任何工作表，提示用户
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(
                self, "提示", 
                "请至少选择一个工作表！"
            )
            return
        
        self.logger.info(f"用户选择了 {len(self.selected_sheets)} 个工作表: {self.selected_sheets}")
        
        # 发出信号并关闭对话框
        self.sheets_selected.emit(self.selected_sheets)
        self.accept()
    
    def _apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #fafafa;
            }
            QGroupBox {
                font-weight: bold;
                border: 1px solid #ddd;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 5px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QCheckBox {
                spacing: 5px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #ccc;
                background-color: white;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #2196F3;
                background-color: #2196F3;
                border-radius: 3px;
            }
            QPushButton {
                padding: 6px 12px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
            QPushButton:hover {
                background-color: #f5f5f5;
                border-color: #999;
            }
            QPushButton:pressed {
                background-color: #e0e0e0;
            }
        """)
    
    def get_selected_sheets(self) -> List[str]:
        """获取选中的工作表列表"""
        return self.selected_sheets.copy()
