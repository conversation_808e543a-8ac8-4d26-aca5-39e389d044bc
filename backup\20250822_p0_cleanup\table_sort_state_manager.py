#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
表级排序状态管理器

针对4类工资表各自独立维护排序状态，解决排序状态在分页切换、导航切换、
数据导入等操作中的保存和恢复问题。

主要功能:
1. 表级排序状态独立管理
2. 排序状态的序列化和恢复
3. 字段映射变更对排序状态的智能适配
4. 分页与排序状态的协调
5. 排序状态持久化存储

创建时间: 2025-07-07
作者: 月度工资异动处理系统开发团队
"""

import time
import json
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path

from src.utils.log_config import setup_logger
from src.utils.logging_utils import bind_context, log_throttle, log_sample, redact
from src.core.data_source_unification_manager import get_global_data_source_manager


class SortStateStatus(Enum):
    """排序状态状态枚举"""
    ACTIVE = "active"          # 活跃状态
    DIRTY = "dirty"            # 已修改，需要保存
    SYNCING = "syncing"        # 同步中
    EXPIRED = "expired"        # 已过期
    ARCHIVED = "archived"      # 已归档


@dataclass
class TableSortState:
    """表级排序状态数据"""
    table_name: str
    table_type: str
    sort_columns: List[Dict[str, Any]] = field(default_factory=list)
    field_mapping: Dict[str, str] = field(default_factory=dict)
    is_global_sort: bool = False  # 是否为全局排序（影响所有分页）
    
    # 状态管理
    status: SortStateStatus = SortStateStatus.ACTIVE
    created_at: float = field(default_factory=time.time)
    last_updated: float = field(default_factory=time.time)
    last_accessed: float = field(default_factory=time.time)
    
    # 分页相关状态
    current_page: int = 1
    page_size: int = 50
    total_records: int = 0
    
    # 会话关联
    session_id: Optional[str] = None
    
    def update_access_time(self):
        """更新最后访问时间"""
        self.last_accessed = time.time()
    
    def mark_dirty(self):
        """标记为已修改"""
        self.status = SortStateStatus.DIRTY
        self.last_updated = time.time()
    
    def is_sort_state_valid(self, current_headers: List[str]) -> bool:
        """检查排序状态是否仍然有效"""
        if not self.sort_columns:
            return True
        
        # 检查排序列是否仍然存在于当前表头中
        for sort_col in self.sort_columns:
            column_name = sort_col.get('column_name', '')
            if column_name and column_name not in current_headers:
                return False
        
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'table_name': self.table_name,
            'table_type': self.table_type,
            'sort_columns': self.sort_columns,
            'field_mapping': self.field_mapping,
            'is_global_sort': self.is_global_sort,
            'status': self.status.value,
            'created_at': self.created_at,
            'last_updated': self.last_updated,
            'last_accessed': self.last_accessed,
            'current_page': self.current_page,
            'page_size': self.page_size,
            'total_records': self.total_records,
            'session_id': self.session_id
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TableSortState':
        """从字典创建对象"""
        state = cls(
            table_name=data.get('table_name', ''),
            table_type=data.get('table_type', ''),
            sort_columns=data.get('sort_columns', []),
            field_mapping=data.get('field_mapping', {}),
            is_global_sort=data.get('is_global_sort', False),
            current_page=data.get('current_page', 1),
            page_size=data.get('page_size', 50),
            total_records=data.get('total_records', 0),
            session_id=data.get('session_id')
        )
        
        # 恢复时间戳
        state.created_at = data.get('created_at', time.time())
        state.last_updated = data.get('last_updated', time.time())
        state.last_accessed = data.get('last_accessed', time.time())
        
        # 恢复状态
        status_str = data.get('status', 'active')
        try:
            state.status = SortStateStatus(status_str)
        except ValueError:
            state.status = SortStateStatus.ACTIVE
        
        return state


class TableSortStateManager:
    """
    表级排序状态管理器
    
    为每个表独立维护排序状态，支持状态的保存、恢复、同步等操作。
    解决排序状态在不同操作间的一致性问题。
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化表级排序状态管理器
        
        Args:
            config_file: 配置文件路径
        """
        base_logger = setup_logger(__name__)
        # 绑定组件级上下文
        self.logger = bind_context(base_logger, component="TableSortStateManager")
        
        # 表排序状态存储
        self.table_sort_states: Dict[str, TableSortState] = {}
        
        # 配置管理
        self.config_file = config_file or "state/sort_states.json"
        self.auto_save = True
        self.max_states = 50  # 最大状态数量
        self.state_ttl_hours = 24  # 状态生存时间（小时）
        
        # 数据源管理器引用
        self.data_source_manager = get_global_data_source_manager()
        
        # 字段映射变更检测
        self.field_mapping_cache: Dict[str, Dict[str, str]] = {}
        
        # 统计信息
        self.total_saves = 0
        self.total_restores = 0
        self.total_cleanups = 0
        self.mapping_adaptations = 0
        
        # 加载现有状态
        self._load_states_from_file()
        
        self.logger.info("表级排序状态管理器初始化完成")
    
    def save_sort_state(
        self, 
        table_name: str, 
        table_type: str,
        sort_columns: List[Dict[str, Any]], 
        field_mapping: Dict[str, str],
        pagination_context: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        保存表的排序状态，感知字段映射
        
        Args:
            table_name: 表名
            table_type: 表类型
            sort_columns: 排序列信息
            field_mapping: 字段映射
            pagination_context: 分页上下文
            
        Returns:
            bool: 保存是否成功
        """
        try:
            # 获取或创建表排序状态
            if table_name in self.table_sort_states:
                state = self.table_sort_states[table_name]
                state.update_access_time()
            else:
                state = TableSortState(
                    table_name=table_name,
                    table_type=table_type
                )
                self.table_sort_states[table_name] = state
            
            # 更新排序状态
            state.sort_columns = sort_columns.copy() if sort_columns else []
            state.field_mapping = field_mapping.copy() if field_mapping else {}
            state.mark_dirty()
            
            # 更新分页上下文
            if pagination_context:
                state.current_page = pagination_context.get('current_page', 1)
                state.page_size = pagination_context.get('page_size', 50)
                state.total_records = pagination_context.get('total_records', 0)
                state.session_id = pagination_context.get('session_id')
            
            # 检测字段映射变更
            self._detect_field_mapping_change(table_name, field_mapping)
            
            # 自动保存到文件
            if self.auto_save:
                self._save_states_to_file()
            
            self.total_saves += 1
            if log_throttle('sort-state-save', 1.0):
                self.logger.info(f"已保存排序状态: {table_name} ({table_type}), {len(sort_columns)} 列")
            
            return True
            
        except Exception as e:
            self.logger.error(f"保存排序状态失败: {e}")
            return False
    
    def restore_sort_state(
        self, 
        table_name: str, 
        current_field_mapping: Optional[Dict[str, str]] = None
    ) -> Optional[TableSortState]:
        """
        恢复表的排序状态，适应字段映射变更
        
        Args:
            table_name: 表名
            current_field_mapping: 当前字段映射
            
        Returns:
            TableSortState: 排序状态，如果不存在返回None
        """
        try:
            if table_name not in self.table_sort_states:
                self.logger.debug(f"表 {table_name} 没有保存的排序状态")
                return None
            
            state = self.table_sort_states[table_name]
            state.update_access_time()
            
            
            # 适应字段映射变更
            if current_field_mapping:
                adapted_state = self._adapt_sort_state_to_mapping(state, current_field_mapping)
                if adapted_state != state:
                    self.table_sort_states[table_name] = adapted_state
                    state = adapted_state
                    self.mapping_adaptations += 1
                    self.logger.info(f"已适配排序状态到新的字段映射: {table_name}")
            
            self.total_restores += 1
            if log_throttle('sort-state-restore', 1.0):
                self.logger.info(f"已恢复排序状态: {table_name}, {len(state.sort_columns)} 列")
            
            return state
            
        except Exception as e:
            self.logger.error(f"恢复排序状态失败: {e}")
            return None
    
    def clear_sort_state(self, table_name: str) -> bool:
        """
        清除表的排序状态
        
        Args:
            table_name: 表名
            
        Returns:
            bool: 清除是否成功
        """
        try:
            if table_name in self.table_sort_states:
                del self.table_sort_states[table_name]
                
                # 自动保存
                if self.auto_save:
                    self._save_states_to_file()
                
                if log_throttle('sort-state-clear', 2.0):
                    self.logger.info(f"已清除排序状态: {table_name}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"清除排序状态失败: {e}")
            return False
    
    def update_sort_after_mapping_change(
        self, 
        table_name: str,
        old_mapping: Dict[str, str], 
        new_mapping: Dict[str, str]
    ) -> bool:
        """
        字段映射变更后更新排序状态
        
        Args:
            table_name: 表名
            old_mapping: 旧字段映射
            new_mapping: 新字段映射
            
        Returns:
            bool: 更新是否成功
        """
        try:
            if table_name not in self.table_sort_states:
                return True  # 没有排序状态，无需更新
            
            state = self.table_sort_states[table_name]
            
            # 检查是否需要更新
            if state.field_mapping == new_mapping:
                return True  # 映射没有变化
            
            # 创建字段名映射转换表
            mapping_transform = {}
            for eng_field, old_chinese in old_mapping.items():
                new_chinese = new_mapping.get(eng_field)
                if new_chinese and old_chinese != new_chinese:
                    mapping_transform[old_chinese] = new_chinese
            
            # 更新排序列中的字段名
            updated_sort_columns = []
            for sort_col in state.sort_columns:
                updated_col = sort_col.copy()
                old_name = sort_col.get('column_name', '')
                
                if old_name in mapping_transform:
                    updated_col['column_name'] = mapping_transform[old_name]
                    if log_sample('sort-mapping-update', 5):
                        self.logger.debug(f"更新排序列字段名: {old_name} -> {mapping_transform[old_name]}")
                
                updated_sort_columns.append(updated_col)
            
            # 更新状态
            state.sort_columns = updated_sort_columns
            state.field_mapping = new_mapping.copy()
            state.mark_dirty()
            
            # 自动保存
            if self.auto_save:
                self._save_states_to_file()
            
            self.mapping_adaptations += 1
            if log_throttle('sort-mapping-update', 2.0):
                self.logger.info(f"已更新排序状态以适应字段映射变更: {table_name}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"更新排序状态失败: {e}")
            return False
    
    def _adapt_sort_state_to_mapping(
        self, 
        state: TableSortState, 
        current_mapping: Dict[str, str]
    ) -> TableSortState:
        """
        适配排序状态到当前字段映射
        
        Args:
            state: 原排序状态
            current_mapping: 当前字段映射
            
        Returns:
            TableSortState: 适配后的排序状态
        """
        try:
            # 如果映射相同，直接返回
            if state.field_mapping == current_mapping:
                return state
            
            # 创建新的状态副本
            adapted_state = TableSortState(
                table_name=state.table_name,
                table_type=state.table_type,
                field_mapping=current_mapping.copy(),
                is_global_sort=state.is_global_sort,
                current_page=state.current_page,
                page_size=state.page_size,
                total_records=state.total_records,
                session_id=state.session_id
            )
            
            # 复制时间戳
            adapted_state.created_at = state.created_at
            adapted_state.last_updated = state.last_updated
            adapted_state.last_accessed = state.last_accessed
            adapted_state.status = state.status
            
            # 适配排序列
            current_headers = list(current_mapping.values())
            adapted_sort_columns = []
            
            for sort_col in state.sort_columns:
                column_name = sort_col.get('column_name', '')
                
                # 检查列名是否仍然有效
                if column_name in current_headers:
                    adapted_sort_columns.append(sort_col.copy())
                else:
                    self.logger.debug(f"排序列 {column_name} 在新映射中不存在，已移除")
            
            adapted_state.sort_columns = adapted_sort_columns
            adapted_state.mark_dirty()
            
            return adapted_state
            
        except Exception as e:
            self.logger.error(f"适配排序状态失败: {e}")
            return state
    
    def _detect_field_mapping_change(self, table_name: str, field_mapping: Dict[str, str]):
        """检测字段映射变更"""
        try:
            old_mapping = self.field_mapping_cache.get(table_name, {})
            
            if old_mapping != field_mapping:
                self.field_mapping_cache[table_name] = field_mapping.copy()
                
                if old_mapping:  # 不是第一次设置
                    self.logger.debug(f"检测到字段映射变更: {table_name}")
                    # 可以在这里触发相关事件
                    
        except Exception as e:
            self.logger.error(f"检测字段映射变更失败: {e}")
    
    def get_sort_state_summary(self) -> Dict[str, Any]:
        """获取排序状态摘要"""
        active_states = {
            name: state for name, state in self.table_sort_states.items()
            if state.status == SortStateStatus.ACTIVE
        }
        
        total_sort_columns = sum(
            len(state.sort_columns) for state in active_states.values()
        )
        
        return {
            'total_tables': len(self.table_sort_states),
            'active_tables': len(active_states),
            'total_sort_columns': total_sort_columns,
            'total_saves': self.total_saves,
            'total_restores': self.total_restores,
            'mapping_adaptations': self.mapping_adaptations,
            'table_details': {
                name: {
                    'table_type': state.table_type,
                    'sort_columns_count': len(state.sort_columns),
                    'has_sort': bool(state.sort_columns),
                    'last_updated': state.last_updated,
                    'status': state.status.value
                }
                for name, state in active_states.items()
            }
        }
    
    def cleanup_expired_states(self):
        """清理过期的排序状态"""
        try:
            current_time = time.time()
            expired_tables = []
            
            for table_name, state in self.table_sort_states.items():
                age_hours = (current_time - state.last_accessed) / 3600
                if age_hours > self.state_ttl_hours:
                    expired_tables.append(table_name)
            
            for table_name in expired_tables:
                del self.table_sort_states[table_name]
            
            if expired_tables:
                self.total_cleanups += len(expired_tables)
                if log_throttle('sort-state-cleanup', 5.0):
                    self.logger.info(f"已清理过期排序状态: {len(expired_tables)} 个表")
                
                # 自动保存
                if self.auto_save:
                    self._save_states_to_file()
                    
        except Exception as e:
            self.logger.error(f"清理过期状态失败: {e}")
    
    def _save_states_to_file(self):
        """保存状态到文件"""
        try:
            # 确保目录存在
            config_path = Path(self.config_file)
            config_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 序列化状态
            states_data = {
                name: state.to_dict() 
                for name, state in self.table_sort_states.items()
            }
            
            # 写入文件
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(states_data, f, ensure_ascii=False, indent=2)
            
            self.logger.debug(f"排序状态已保存到文件: {config_path}")
            
        except Exception as e:
            self.logger.error(f"保存状态到文件失败: {e}")
    
    def _load_states_from_file(self):
        """从文件加载状态"""
        try:
            config_path = Path(self.config_file)
            
            if not config_path.exists():
                self.logger.debug("排序状态文件不存在，将创建新文件")
                return
            
            with open(config_path, 'r', encoding='utf-8') as f:
                states_data = json.load(f)
            
            # 反序列化状态
            for name, state_dict in states_data.items():
                try:
                    state = TableSortState.from_dict(state_dict)
                    self.table_sort_states[name] = state
                except Exception as e:
                    self.logger.error(f"加载表 {name} 的排序状态失败: {e}")
            
            self.logger.info(f"已从文件加载排序状态: {len(self.table_sort_states)} 个表")
            
        except Exception as e:
            self.logger.error(f"从文件加载状态失败: {e}")


# 全局单例实例
_global_sort_state_manager = None

def get_global_sort_state_manager() -> TableSortStateManager:
    """
    获取全局表级排序状态管理器实例
    
    Returns:
        TableSortStateManager: 全局实例
    """
    global _global_sort_state_manager
    if _global_sort_state_manager is None:
        _global_sort_state_manager = TableSortStateManager()
    return _global_sort_state_manager


if __name__ == "__main__":
    # 测试代码
    print("测试表级排序状态管理器...")
    
    manager = TableSortStateManager()
    
    # 测试保存排序状态
    sort_columns = [
        {
            'column_name': '工资',
            'order': 'descending',
            'priority': 0,
            'column_index': 2
        }
    ]
    
    field_mapping = {
        'salary': '工资',
        'name': '姓名'
    }
    
    success = manager.save_sort_state(
        table_name="test_table",
        table_type="A岗职工表",
        sort_columns=sort_columns,
        field_mapping=field_mapping
    )
    
    print(f"保存排序状态: {success}")
    
    # 测试恢复排序状态
    restored_state = manager.restore_sort_state("test_table")
    if restored_state:
        print(f"恢复排序状态: {restored_state.table_type}, {len(restored_state.sort_columns)} 列")
    
    # 获取摘要
    summary = manager.get_sort_state_summary()
    print(f"状态摘要: {summary}")
    
    print("表级排序状态管理器测试完成!")