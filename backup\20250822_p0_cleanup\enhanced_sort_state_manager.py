"""
增强的排序状态管理器 - P0级修复
解决排序状态在分页切换时丢失的问题
"""

import json
import threading
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
from loguru import logger


@dataclass
class SortColumn:
    """排序列配置"""
    column_name: str
    sort_order: str  # 'asc' or 'desc'
    priority: int = 0  # 多列排序时的优先级
    
    def to_dict(self) -> Dict:
        return {
            'column_name': self.column_name,
            'sort_order': self.sort_order,
            'priority': self.priority
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'SortColumn':
        return cls(
            column_name=data.get('column_name', ''),
            sort_order=data.get('sort_order', 'asc'),
            priority=data.get('priority', 0)
        )


@dataclass
class TableSortState:
    """表的排序状态"""
    table_name: str
    sort_columns: List[SortColumn] = field(default_factory=list)
    last_updated: datetime = field(default_factory=datetime.now)
    is_active: bool = True
    
    def to_dict(self) -> Dict:
        return {
            'table_name': self.table_name,
            'sort_columns': [col.to_dict() for col in self.sort_columns],
            'last_updated': self.last_updated.isoformat(),
            'is_active': self.is_active
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'TableSortState':
        return cls(
            table_name=data.get('table_name', ''),
            sort_columns=[SortColumn.from_dict(c) for c in data.get('sort_columns', [])],
            last_updated=datetime.fromisoformat(data.get('last_updated', datetime.now().isoformat())),
            is_active=data.get('is_active', True)
        )


class EnhancedSortStateManager:
    """
    增强的排序状态管理器
    
    主要功能：
    1. 排序状态持久化
    2. 跨分页保持排序
    3. 表切换时自动恢复
    4. 排序状态验证
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化管理器"""
        if not hasattr(self, '_initialized'):
            self.logger = logger
            self._state_lock = threading.RLock()
            
            # 内存状态存储
            self._table_states: Dict[str, TableSortState] = {}
            
            # 持久化配置
            self._state_dir = Path('state/sort')
            self._state_dir.mkdir(parents=True, exist_ok=True)
            self._state_file = self._state_dir / 'sort_states.json'
            
            # 加载已保存的状态
            self._load_states()
            
            self._initialized = True
            self.logger.info("EnhancedSortStateManager 初始化完成")
    
    def save_sort_state(self, table_name: str, sort_columns: List[Dict[str, Any]]) -> bool:
        """
        保存排序状态
        
        Args:
            table_name: 表名
            sort_columns: 排序列配置
        
        Returns:
            是否保存成功
        """
        try:
            with self._state_lock:
                # 转换为内部格式
                sort_cols = []
                for i, col in enumerate(sort_columns):
                    if isinstance(col, dict):
                        sort_cols.append(SortColumn(
                            column_name=col.get('column_name', col.get('column', '')),
                            sort_order=col.get('sort_order', col.get('order', 'asc')),
                            priority=i
                        ))
                
                # 创建或更新状态
                state = TableSortState(
                    table_name=table_name,
                    sort_columns=sort_cols,
                    last_updated=datetime.now(),
                    is_active=True
                )
                
                self._table_states[table_name] = state
                
                # 持久化
                self._persist_states()
                
                self.logger.info(f"保存排序状态成功: {table_name}, 列数={len(sort_cols)}")
                return True
                
        except Exception as e:
            self.logger.error(f"保存排序状态失败: {e}")
            return False
    
    def get_sort_state(self, table_name: str) -> Optional[List[Dict[str, Any]]]:
        """
        获取排序状态
        
        Args:
            table_name: 表名
        
        Returns:
            排序列配置列表
        """
        try:
            with self._state_lock:
                state = self._table_states.get(table_name)
                
                if state and state.is_active:
                    # 转换为标准格式
                    return [col.to_dict() for col in state.sort_columns]
                
                return None
                
        except Exception as e:
            self.logger.error(f"获取排序状态失败: {e}")
            return None
    
    def clear_sort_state(self, table_name: str) -> bool:
        """
        清除排序状态
        
        Args:
            table_name: 表名
        
        Returns:
            是否清除成功
        """
        try:
            with self._state_lock:
                if table_name in self._table_states:
                    self._table_states[table_name].is_active = False
                    self._table_states[table_name].sort_columns = []
                    self._persist_states()
                    self.logger.info(f"清除排序状态: {table_name}")
                return True
                
        except Exception as e:
            self.logger.error(f"清除排序状态失败: {e}")
            return False
    
    def validate_sort_state(self, table_name: str, available_columns: List[str]) -> bool:
        """
        验证排序状态是否有效
        
        Args:
            table_name: 表名
            available_columns: 可用的列名列表
        
        Returns:
            排序状态是否有效
        """
        try:
            state = self._table_states.get(table_name)
            
            if not state or not state.sort_columns:
                return True  # 没有排序也是有效的
            
            # 检查所有排序列是否存在
            for sort_col in state.sort_columns:
                if sort_col.column_name not in available_columns:
                    self.logger.warning(f"排序列不存在: {sort_col.column_name}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证排序状态失败: {e}")
            return False
    
    def update_sort_column(self, table_name: str, column_name: str, 
                          sort_order: Optional[str] = None) -> bool:
        """
        更新单个列的排序状态
        
        Args:
            table_name: 表名
            column_name: 列名
            sort_order: 排序顺序，None表示切换
        
        Returns:
            是否更新成功
        """
        try:
            with self._state_lock:
                state = self._table_states.get(table_name)
                
                if not state:
                    # 创建新状态
                    state = TableSortState(table_name=table_name)
                    self._table_states[table_name] = state
                
                # 查找列
                existing_col = None
                for col in state.sort_columns:
                    if col.column_name == column_name:
                        existing_col = col
                        break
                
                if existing_col:
                    if sort_order is None:
                        # 切换排序顺序
                        existing_col.sort_order = 'desc' if existing_col.sort_order == 'asc' else 'asc'
                    elif sort_order in ['asc', 'desc']:
                        existing_col.sort_order = sort_order
                    else:
                        # 移除排序
                        state.sort_columns.remove(existing_col)
                else:
                    # 添加新的排序列
                    if sort_order != 'none':
                        state.sort_columns.append(SortColumn(
                            column_name=column_name,
                            sort_order=sort_order or 'asc',
                            priority=len(state.sort_columns)
                        ))
                
                state.last_updated = datetime.now()
                self._persist_states()
                
                self.logger.info(f"更新排序列: {table_name}.{column_name} -> {sort_order}")
                return True
                
        except Exception as e:
            self.logger.error(f"更新排序列失败: {e}")
            return False
    
    def get_active_tables(self) -> List[str]:
        """获取有活跃排序状态的表列表"""
        with self._state_lock:
            return [name for name, state in self._table_states.items() 
                   if state.is_active and state.sort_columns]
    
    def _persist_states(self):
        """持久化状态到文件"""
        try:
            states_data = {
                name: state.to_dict() 
                for name, state in self._table_states.items()
            }
            
            with open(self._state_file, 'w', encoding='utf-8') as f:
                json.dump(states_data, f, ensure_ascii=False, indent=2)
            
            self.logger.debug(f"持久化排序状态: {len(states_data)} 个表")
            
        except Exception as e:
            self.logger.error(f"持久化排序状态失败: {e}")
    
    def _load_states(self):
        """从文件加载状态"""
        try:
            if self._state_file.exists():
                with open(self._state_file, 'r', encoding='utf-8') as f:
                    states_data = json.load(f)
                
                for name, data in states_data.items():
                    self._table_states[name] = TableSortState.from_dict(data)
                
                self.logger.info(f"加载排序状态: {len(self._table_states)} 个表")
            
        except Exception as e:
            self.logger.error(f"加载排序状态失败: {e}")
            self._table_states = {}
    
    def clear_all_states(self):
        """清除所有排序状态"""
        with self._state_lock:
            self._table_states.clear()
            if self._state_file.exists():
                self._state_file.unlink()
            self.logger.info("清除所有排序状态")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._state_lock:
            active_count = sum(1 for s in self._table_states.values() if s.is_active)
            total_columns = sum(len(s.sort_columns) for s in self._table_states.values())
            
            return {
                'total_tables': len(self._table_states),
                'active_tables': active_count,
                'total_sort_columns': total_columns,
                'state_file_size': self._state_file.stat().st_size if self._state_file.exists() else 0
            }


# 全局实例获取函数
def get_sort_state_manager() -> EnhancedSortStateManager:
    """获取排序状态管理器的全局实例"""
    return EnhancedSortStateManager()