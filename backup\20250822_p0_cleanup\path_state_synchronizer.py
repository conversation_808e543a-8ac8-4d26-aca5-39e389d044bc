#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
路径状态同步器

负责在不同数据展示路径之间同步状态信息，确保用户在导航路径和分页路径之间切换时，
排序状态、字段映射、过滤条件等保持一致。

主要功能:
1. 导航路径到分页路径的状态同步
2. 分页路径到导航路径的状态同步
3. 排序状态的跨路径继承
4. 字段映射状态的一致性保证
5. 分页状态的智能传递

创建时间: 2025-07-07
作者: 月度工资异动处理系统开发团队
"""

import time
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass, field
from enum import Enum

from src.utils.log_config import setup_logger
from src.utils.logging_utils import bind_context, log_throttle, log_sample, redact
from src.core.data_source_unification_manager import (
    DataPath, 
    PathContext,
    get_global_data_source_manager
)


class SyncResult(Enum):
    """同步结果枚举"""
    SUCCESS = "success"           # 同步成功
    PARTIAL_SUCCESS = "partial"   # 部分同步成功
    FAILED = "failed"            # 同步失败
    SKIPPED = "skipped"          # 跳过同步


@dataclass
class SyncOperation:
    """同步操作记录"""
    operation_id: str
    source_path: DataPath
    target_path: DataPath
    table_name: str
    session_id: str
    sync_items: List[str]  # 同步的项目列表
    result: SyncResult
    start_time: float
    end_time: float = 0.0
    error_message: str = ""
    
    @property
    def duration(self) -> float:
        """获取同步耗时（秒）"""
        return self.end_time - self.start_time if self.end_time > 0 else 0.0


class PathStateSynchronizer:
    """
    路径状态同步器
    
    负责在导航路径和分页路径之间同步状态信息，
    确保用户体验的一致性和数据的完整性。
    """
    
    def __init__(self):
        """初始化路径状态同步器"""
        base_logger = setup_logger(__name__)
        # 绑定组件级上下文
        self.logger = bind_context(base_logger, component="PathStateSynchronizer")
        self.data_source_manager = get_global_data_source_manager()
        
        # 同步操作记录
        self.sync_operations: List[SyncOperation] = []
        self.max_operation_history = 100
        
        # 同步配置
        self.enable_sort_sync = True
        self.enable_field_mapping_sync = True
        self.enable_pagination_sync = True
        self.enable_filter_sync = True
        
        # 统计信息
        self.total_syncs = 0
        self.successful_syncs = 0
        self.failed_syncs = 0
        
        self.logger.info("路径状态同步器初始化完成")
    
    def sync_navigation_to_pagination(
        self, 
        nav_context: Dict[str, Any], 
        pagination_widget
    ) -> Dict[str, Any]:
        """
        同步导航状态到分页组件
        
        Args:
            nav_context: 导航路径上下文
            pagination_widget: 分页组件实例
            
        Returns:
            Dict: 同步结果信息
        """
        operation_id = f"nav_to_pag_{int(time.time())}_{id(pagination_widget)}"
        sync_op = SyncOperation(
            operation_id=operation_id,
            source_path=DataPath.NAVIGATION,
            target_path=DataPath.PAGINATION,
            table_name=nav_context.get('table_name', ''),
            session_id=nav_context.get('session_id', ''),
            sync_items=[],
            result=SyncResult.FAILED,
            start_time=time.time()
        )
        
        try:
            self.total_syncs += 1
            sync_result = {'synced_items': [], 'errors': []}
            
            # 1. 同步排序状态
            if self.enable_sort_sync and 'sort_state' in nav_context:
                try:
                    sort_success = self._sync_sort_state_to_pagination(
                        nav_context['sort_state'], 
                        pagination_widget
                    )
                    if sort_success:
                        sync_result['synced_items'].append('sort_state')
                        sync_op.sync_items.append('sort_state')
                        if log_sample('sync-sort', 5):
                            self.logger.debug("排序状态同步成功：导航 -> 分页")
                    else:
                        sync_result['errors'].append("排序状态同步失败")
                except Exception as e:
                    sync_result['errors'].append(f"排序状态同步异常: {e}")
                    self.logger.error(f"排序状态同步异常: {e}")
            
            # 2. 同步字段映射状态
            if self.enable_field_mapping_sync and 'field_mapping' in nav_context:
                try:
                    mapping_success = self._sync_field_mapping_to_pagination(
                        nav_context['field_mapping'],
                        pagination_widget
                    )
                    if mapping_success:
                        sync_result['synced_items'].append('field_mapping')
                        sync_op.sync_items.append('field_mapping')
                        if log_sample('sync-mapping', 5):
                            self.logger.debug("字段映射同步成功：导航 -> 分页")
                    else:
                        sync_result['errors'].append("字段映射同步失败")
                except Exception as e:
                    sync_result['errors'].append(f"字段映射同步异常: {e}")
                    self.logger.error(f"字段映射同步异常: {e}")
            
            # 3. 同步分页状态
            if self.enable_pagination_sync:
                try:
                    pagination_success = self._sync_pagination_state_to_pagination(
                        nav_context,
                        pagination_widget
                    )
                    if pagination_success:
                        sync_result['synced_items'].append('pagination_state')
                        sync_op.sync_items.append('pagination_state')
                        if log_sample('sync-pagination', 5):
                            self.logger.debug("分页状态同步成功：导航 -> 分页")
                    else:
                        sync_result['errors'].append("分页状态同步失败")
                except Exception as e:
                    sync_result['errors'].append(f"分页状态同步异常: {e}")
                    self.logger.error(f"分页状态同步异常: {e}")
            
            # 4. 同步数据源上下文
            try:
                context_success = self._sync_data_source_context_to_pagination(
                    nav_context,
                    pagination_widget
                )
                if context_success:
                    sync_result['synced_items'].append('data_source_context')
                    sync_op.sync_items.append('data_source_context')
                    if log_sample('sync-datasource', 5):
                        self.logger.debug("数据源上下文同步成功：导航 -> 分页")
            except Exception as e:
                sync_result['errors'].append(f"数据源上下文同步异常: {e}")
                self.logger.error(f"数据源上下文同步异常: {e}")
            
            # 确定同步结果
            if len(sync_result['errors']) == 0:
                sync_op.result = SyncResult.SUCCESS
                self.successful_syncs += 1
            elif len(sync_result['synced_items']) > 0:
                sync_op.result = SyncResult.PARTIAL_SUCCESS
                self.successful_syncs += 1
            else:
                sync_op.result = SyncResult.FAILED
                self.failed_syncs += 1
            
            sync_op.end_time = time.time()
            self._add_sync_operation(sync_op)
            
            if log_throttle('sync-summary', 1.0):
                self.logger.info(
                    f"导航->分页同步完成: {sync_op.result.value}, "
                    f"同步项: {len(sync_result['synced_items'])}, "
                    f"错误: {len(sync_result['errors'])}"
                )
            
            return sync_result
            
        except Exception as e:
            sync_op.result = SyncResult.FAILED
            sync_op.error_message = str(e)
            sync_op.end_time = time.time()
            self._add_sync_operation(sync_op)
            self.failed_syncs += 1
            
            self.logger.error(f"导航到分页同步失败: {e}")
            return {'synced_items': [], 'errors': [str(e)]}
    
    def sync_pagination_to_navigation(
        self, 
        pagination_context: Dict[str, Any], 
        navigation_widget
    ) -> Dict[str, Any]:
        """
        同步分页状态到导航组件
        
        Args:
            pagination_context: 分页路径上下文
            navigation_widget: 导航组件实例
            
        Returns:
            Dict: 同步结果信息
        """
        operation_id = f"pag_to_nav_{int(time.time())}_{id(navigation_widget)}"
        sync_op = SyncOperation(
            operation_id=operation_id,
            source_path=DataPath.PAGINATION,
            target_path=DataPath.NAVIGATION,
            table_name=pagination_context.get('table_name', ''),
            session_id=pagination_context.get('session_id', ''),
            sync_items=[],
            result=SyncResult.FAILED,
            start_time=time.time()
        )
        
        try:
            self.total_syncs += 1
            sync_result = {'synced_items': [], 'errors': []}
            
            # 1. 同步排序状态
            if self.enable_sort_sync and 'sort_state' in pagination_context:
                try:
                    sort_success = self._sync_sort_state_to_navigation(
                        pagination_context['sort_state'],
                        navigation_widget
                    )
                    if sort_success:
                        sync_result['synced_items'].append('sort_state')
                        sync_op.sync_items.append('sort_state')
                        if log_sample('sync-sort', 5):
                            self.logger.debug("排序状态同步成功：分页 -> 导航")
                except Exception as e:
                    sync_result['errors'].append(f"排序状态同步异常: {e}")
                    self.logger.error(f"排序状态同步异常: {e}")
            
            # 2. 同步字段映射状态
            if self.enable_field_mapping_sync and 'field_mapping' in pagination_context:
                try:
                    mapping_success = self._sync_field_mapping_to_navigation(
                        pagination_context['field_mapping'],
                        navigation_widget
                    )
                    if mapping_success:
                        sync_result['synced_items'].append('field_mapping')
                        sync_op.sync_items.append('field_mapping')
                        if log_sample('sync-mapping', 5):
                            self.logger.debug("字段映射同步成功：分页 -> 导航")
                except Exception as e:
                    sync_result['errors'].append(f"字段映射同步异常: {e}")
                    self.logger.error(f"字段映射同步异常: {e}")
            
            # 3. 同步过滤状态
            if self.enable_filter_sync and 'filter_state' in pagination_context:
                try:
                    filter_success = self._sync_filter_state_to_navigation(
                        pagination_context['filter_state'],
                        navigation_widget
                    )
                    if filter_success:
                        sync_result['synced_items'].append('filter_state')
                        sync_op.sync_items.append('filter_state')
                        if log_sample('sync-filter', 5):
                            self.logger.debug("过滤状态同步成功：分页 -> 导航")
                except Exception as e:
                    sync_result['errors'].append(f"过滤状态同步异常: {e}")
                    self.logger.error(f"过滤状态同步异常: {e}")
            
            # 确定同步结果
            if len(sync_result['errors']) == 0:
                sync_op.result = SyncResult.SUCCESS
                self.successful_syncs += 1
            elif len(sync_result['synced_items']) > 0:
                sync_op.result = SyncResult.PARTIAL_SUCCESS
                self.successful_syncs += 1
            else:
                sync_op.result = SyncResult.FAILED
                self.failed_syncs += 1
            
            sync_op.end_time = time.time()
            self._add_sync_operation(sync_op)
            
            if log_throttle('sync-summary', 1.0):
                self.logger.info(
                    f"分页->导航同步完成: {sync_op.result.value}, "
                    f"同步项: {len(sync_result['synced_items'])}, "
                    f"错误: {len(sync_result['errors'])}"
                )
            
            return sync_result
            
        except Exception as e:
            sync_op.result = SyncResult.FAILED
            sync_op.error_message = str(e)
            sync_op.end_time = time.time()
            self._add_sync_operation(sync_op)
            self.failed_syncs += 1
            
            self.logger.error(f"分页到导航同步失败: {e}")
            return {'synced_items': [], 'errors': [str(e)]}
    
    def _sync_sort_state_to_pagination(
        self, 
        sort_state: Dict[str, Any], 
        pagination_widget
    ) -> bool:
        """同步排序状态到分页组件"""
        try:
            if hasattr(pagination_widget, 'apply_inherited_sort_state'):
                pagination_widget.apply_inherited_sort_state(sort_state)
                return True
            elif hasattr(pagination_widget, 'set_sort_state'):
                pagination_widget.set_sort_state(sort_state)
                return True
            else:
                self.logger.warning("分页组件不支持排序状态设置")
                return False
        except Exception as e:
            self.logger.error(f"同步排序状态到分页组件失败: {e}")
            return False
    
    def _sync_field_mapping_to_pagination(
        self, 
        field_mapping: Dict[str, str], 
        pagination_widget
    ) -> bool:
        """同步字段映射到分页组件"""
        try:
            if hasattr(pagination_widget, 'apply_inherited_field_mapping'):
                pagination_widget.apply_inherited_field_mapping(field_mapping)
                return True
            elif hasattr(pagination_widget, 'set_field_mapping'):
                pagination_widget.set_field_mapping(field_mapping)
                return True
            else:
                self.logger.warning("分页组件不支持字段映射设置")
                return False
        except Exception as e:
            self.logger.error(f"同步字段映射到分页组件失败: {e}")
            return False
    
    def _sync_pagination_state_to_pagination(
        self, 
        nav_context: Dict[str, Any], 
        pagination_widget
    ) -> bool:
        """同步分页状态到分页组件"""
        try:
            # 提取分页相关信息
            pagination_info = {
                'current_page': nav_context.get('current_page', 1),
                'page_size': nav_context.get('page_size', 50),
                'total_records': nav_context.get('total_records', 0)
            }
            
            if hasattr(pagination_widget, 'set_pagination_info'):
                pagination_widget.set_pagination_info(pagination_info)
                return True
            elif hasattr(pagination_widget, 'update_pagination_state'):
                pagination_widget.update_pagination_state(pagination_info)
                return True
            else:
                self.logger.warning("分页组件不支持分页状态设置")
                return False
        except Exception as e:
            self.logger.error(f"同步分页状态到分页组件失败: {e}")
            return False
    
    def _sync_data_source_context_to_pagination(
        self, 
        nav_context: Dict[str, Any], 
        pagination_widget
    ) -> bool:
        """同步数据源上下文到分页组件"""
        try:
            data_source_context = {
                'session_id': nav_context.get('session_id'),
                'table_name': nav_context.get('table_name'),
                'table_type': nav_context.get('table_type'),
                'data_source': nav_context.get('data_source')
            }
            
            if hasattr(pagination_widget, 'set_data_source_context'):
                pagination_widget.set_data_source_context(data_source_context)
                return True
            else:
                self.logger.debug("分页组件不支持数据源上下文设置")
                return False
        except Exception as e:
            self.logger.error(f"同步数据源上下文到分页组件失败: {e}")
            return False
    
    def _sync_sort_state_to_navigation(
        self, 
        sort_state: Dict[str, Any], 
        navigation_widget
    ) -> bool:
        """同步排序状态到导航组件"""
        try:
            if hasattr(navigation_widget, 'apply_sort_state'):
                navigation_widget.apply_sort_state(sort_state)
                return True
            elif hasattr(navigation_widget, 'set_sort_state'):
                navigation_widget.set_sort_state(sort_state)
                return True
            else:
                self.logger.warning("导航组件不支持排序状态设置")
                return False
        except Exception as e:
            self.logger.error(f"同步排序状态到导航组件失败: {e}")
            return False
    
    def _sync_field_mapping_to_navigation(
        self, 
        field_mapping: Dict[str, str], 
        navigation_widget
    ) -> bool:
        """同步字段映射到导航组件"""
        try:
            if hasattr(navigation_widget, 'apply_field_mapping'):
                navigation_widget.apply_field_mapping(field_mapping)
                return True
            elif hasattr(navigation_widget, 'set_field_mapping'):
                navigation_widget.set_field_mapping(field_mapping)
                return True
            else:
                self.logger.warning("导航组件不支持字段映射设置")
                return False
        except Exception as e:
            self.logger.error(f"同步字段映射到导航组件失败: {e}")
            return False
    
    def _sync_filter_state_to_navigation(
        self, 
        filter_state: Dict[str, Any], 
        navigation_widget
    ) -> bool:
        """同步过滤状态到导航组件"""
        try:
            if hasattr(navigation_widget, 'apply_filter_state'):
                navigation_widget.apply_filter_state(filter_state)
                return True
            elif hasattr(navigation_widget, 'set_filter_state'):
                navigation_widget.set_filter_state(filter_state)
                return True
            else:
                self.logger.warning("导航组件不支持过滤状态设置")
                return False
        except Exception as e:
            self.logger.error(f"同步过滤状态到导航组件失败: {e}")
            return False
    
    def _add_sync_operation(self, sync_op: SyncOperation):
        """添加同步操作记录"""
        self.sync_operations.append(sync_op)
        
        # 限制历史记录数量
        if len(self.sync_operations) > self.max_operation_history:
            self.sync_operations = self.sync_operations[-self.max_operation_history:]
    
    def get_recent_sync_operations(self, count: int = 10) -> List[SyncOperation]:
        """
        获取最近的同步操作记录
        
        Args:
            count: 返回的记录数量
            
        Returns:
            List[SyncOperation]: 最近的同步操作记录
        """
        return self.sync_operations[-count:] if self.sync_operations else []
    
    def get_sync_statistics(self) -> Dict[str, Any]:
        """
        获取同步统计信息
        
        Returns:
            Dict: 统计信息
        """
        success_rate = (self.successful_syncs / self.total_syncs * 100) if self.total_syncs > 0 else 0
        
        recent_operations = self.get_recent_sync_operations(10)
        avg_duration = 0
        if recent_operations:
            total_duration = sum(op.duration for op in recent_operations if op.duration > 0)
            avg_duration = total_duration / len(recent_operations)
        
        return {
            'total_syncs': self.total_syncs,
            'successful_syncs': self.successful_syncs,
            'failed_syncs': self.failed_syncs,
            'success_rate': round(success_rate, 2),
            'average_duration_ms': round(avg_duration * 1000, 2),
            'operation_history_count': len(self.sync_operations),
            'sync_config': {
                'sort_sync_enabled': self.enable_sort_sync,
                'field_mapping_sync_enabled': self.enable_field_mapping_sync,
                'pagination_sync_enabled': self.enable_pagination_sync,
                'filter_sync_enabled': self.enable_filter_sync
            }
        }
    
    def reset_statistics(self):
        """重置统计信息"""
        self.total_syncs = 0
        self.successful_syncs = 0
        self.failed_syncs = 0
        self.sync_operations.clear()
        self.logger.info("同步统计信息已重置")


# 全局单例实例
_global_path_synchronizer = None

def get_global_path_synchronizer() -> PathStateSynchronizer:
    """
    获取全局路径状态同步器实例
    
    Returns:
        PathStateSynchronizer: 全局实例
    """
    global _global_path_synchronizer
    if _global_path_synchronizer is None:
        _global_path_synchronizer = PathStateSynchronizer()
    return _global_path_synchronizer


if __name__ == "__main__":
    # 测试代码
    print("测试路径状态同步器...")
    
    synchronizer = PathStateSynchronizer()
    
    # 模拟导航上下文
    nav_context = {
        'table_name': 'test_table',
        'session_id': 'test_session_123',
        'sort_state': {
            'column': 'salary',
            'order': 'desc',
            'priority': 0
        },
        'field_mapping': {
            'name': '姓名',
            'salary': '工资'
        },
        'current_page': 1,
        'page_size': 50
    }
    
    # 模拟分页组件
    class MockPaginationWidget:
        def apply_inherited_sort_state(self, sort_state):
            print(f"应用排序状态: {sort_state}")
        
        def apply_inherited_field_mapping(self, field_mapping):
            print(f"应用字段映射: {field_mapping}")
        
        def set_pagination_info(self, pagination_info):
            print(f"设置分页信息: {pagination_info}")
    
    mock_pagination = MockPaginationWidget()
    
    # 测试同步
    result = synchronizer.sync_navigation_to_pagination(nav_context, mock_pagination)
    print(f"同步结果: {result}")
    
    # 获取统计信息
    stats = synchronizer.get_sync_statistics()
    print(f"统计信息: {stats}")
    
    print("路径状态同步器测试完成!")