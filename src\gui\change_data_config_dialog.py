"""
异动表配置对话框
提供可视化的字段类型配置和格式化规则设置界面
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGroupBox, QPushButton,
    QTableWidget, QTableWidgetItem, QComboBox, QLabel, QLineEdit,
    QTextEdit, QSplitter, QWidget, QHeaderView, QMessageBox,
    QFileDialog, QSpinBox, QCheckBox, QTabWidget, QFormLayout,
    QDialogButtonBox, QInputDialog, QMenu, QAction
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor
import pandas as pd
import json
from typing import Dict, List, Optional, Any, Tuple
from loguru import logger
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from src.modules.data_import.change_data_config_manager import ChangeDataConfigManager
from src.modules.data_import.formatting_engine import get_formatting_engine
from src.modules.data_import.field_type_manager import FieldTypeManager
from src.gui.widgets.multi_sheet_selection_dialog import MultiSheetSelectionDialog


class ChangeDataConfigDialog(QDialog):
    """异动表配置对话框"""
    
    # 配置完成信号
    config_saved = pyqtSignal(dict)
    
    def __init__(self, excel_data: pd.DataFrame = None, excel_file_path: str = None, parent=None):
        """
        初始化配置对话框
        
        Args:
            excel_data: Excel数据（用于预览）
            excel_file_path: Excel文件路径（用于读取多个sheet）
            parent: 父窗口
        """
        super().__init__(parent)
        self.excel_data = excel_data
        self.excel_file_path = excel_file_path
        self.config_manager = ChangeDataConfigManager()
        self.formatting_engine = get_formatting_engine()  # 获取格式化引擎
        self.field_type_manager = FieldTypeManager()  # 字段类型管理器
        self.current_config = {}
        self.all_sheets_data = {}  # 存储所有sheet的数据
        self.current_sheet_name = 'unknown'  # 当前工作表名称，用于自动保存配置
        self.all_sheets_configs = {}  # 存储所有工作表的配置
        
        # 如果提供了文件路径，加载所有sheets
        if self.excel_file_path:
            self.load_all_sheets()
        
        self.init_ui()
        
        # 如果有Excel数据，加载到预览区
        if self.excel_data is not None:
            self.load_excel_preview()
        elif self.all_sheets_data:
            # 如果有多sheet数据，加载第一个sheet
            first_sheet = list(self.all_sheets_data.keys())[0]
            self.excel_data = self.all_sheets_data[first_sheet]
            self.current_sheet_name = first_sheet  # 设置当前工作表名称
            self.load_excel_preview()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("异动表字段配置")
        self.setMinimumSize(1200, 800)
        
        # 主布局
        main_layout = QVBoxLayout()
        
        # 顶部工具栏
        toolbar = self.create_toolbar()
        main_layout.addWidget(toolbar)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 字段配置标签页
        field_config_tab = self.create_field_config_tab()
        self.tab_widget.addTab(field_config_tab, "字段配置")
        
        # 格式化规则标签页
        format_rules_tab = self.create_format_rules_tab()
        self.tab_widget.addTab(format_rules_tab, "格式化规则")
        
        # 预览标签页
        preview_tab = self.create_preview_tab()
        self.tab_widget.addTab(preview_tab, "数据预览")
        
        main_layout.addWidget(self.tab_widget)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.save_config_btn = QPushButton("另存配置")
        self.save_config_btn.setToolTip("将所有已配置工作表的字段配置保存为独立文件（一次性保存多表）")
        self.save_config_btn.clicked.connect(self.save_configuration)
        
        self.apply_btn = QPushButton("应用")
        self.apply_btn.setToolTip("将当前配置应用到系统，并自动保存")
        self.apply_btn.clicked.connect(self.apply_configuration)
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.save_config_btn)
        button_layout.addWidget(self.apply_btn)
        button_layout.addWidget(self.cancel_btn)
        
        main_layout.addLayout(button_layout)
        
        self.setLayout(main_layout)
    
    def create_toolbar(self) -> QWidget:
        """创建工具栏"""
        toolbar = QWidget()
        layout = QHBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Sheet选择（如果有多个sheet）
        if self.all_sheets_data and len(self.all_sheets_data) > 1:
            layout.addWidget(QLabel("选择工作表:"))
            self.sheet_combo = QComboBox()
            self.sheet_combo.setMinimumWidth(150)
            self.sheet_combo.addItems(list(self.all_sheets_data.keys()))
            self.sheet_combo.currentTextChanged.connect(self.on_sheet_changed)
            layout.addWidget(self.sheet_combo)
            layout.addWidget(QLabel("|"))  # 分隔符
        
        # 配置选择
        layout.addWidget(QLabel("选择配置:"))
        self.config_combo = QComboBox()
        self.config_combo.setMinimumWidth(200)
        self.update_config_list()
        self.config_combo.currentTextChanged.connect(self.load_selected_config)
        layout.addWidget(self.config_combo)
        
        # 模板选择
        layout.addWidget(QLabel("应用模板:"))
        self.template_combo = QComboBox()
        self.template_combo.setMinimumWidth(200)
        self.update_template_list()
        self.template_combo.currentIndexChanged.connect(self.apply_template)
        layout.addWidget(self.template_combo)
        
        # 添加模板预览按钮
        self.preview_template_btn = QPushButton("👁️ 预览")
        self.preview_template_btn.setMaximumWidth(60)
        self.preview_template_btn.clicked.connect(self.preview_template)
        self.preview_template_btn.setToolTip("预览模板内容")
        layout.addWidget(self.preview_template_btn)
        
        layout.addStretch()
        
        # 操作按钮
        self.wizard_btn = QPushButton("🧙 配置向导")
        self.wizard_btn.clicked.connect(self.open_config_wizard)
        self.wizard_btn.setToolTip("使用向导快速配置")
        layout.addWidget(self.wizard_btn)
        
        # 添加字段类型管理按钮
        self.manage_types_btn = QPushButton("⚙️ 管理字段类型")
        self.manage_types_btn.clicked.connect(self.manage_field_types)
        self.manage_types_btn.setToolTip("创建和管理自定义字段类型")
        layout.addWidget(self.manage_types_btn)
        
        self.import_btn = QPushButton("导入配置")
        self.import_btn.clicked.connect(self.import_configuration)
        layout.addWidget(self.import_btn)

        self.delete_btn = QPushButton("删除配置")
        self.delete_btn.clicked.connect(self.delete_configuration)
        layout.addWidget(self.delete_btn)
        
        toolbar.setLayout(layout)
        return toolbar
    
    def create_field_config_tab(self) -> QWidget:
        """创建字段配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # 说明文本
        info_label = QLabel("配置每个字段的类型，系统将根据字段类型自动应用相应的格式化规则。")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 字段配置表格
        self.field_table = QTableWidget()
        self.field_table.setColumnCount(4)
        self.field_table.setHorizontalHeaderLabels(["Excel字段", "字段类型", "数据库字段", "示例数据"])
        
        # 设置列宽
        header = self.field_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Fixed)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        
        self.field_table.setColumnWidth(1, 200)  # 增加下拉框列的宽度
        
        # 🔧 [P1修复] 设置更合理的行高以确保下拉框正常显示
        self.field_table.verticalHeader().setDefaultSectionSize(50)  # 适中的行高
        self.field_table.verticalHeader().setMinimumSectionSize(50)  # 设置最小行高
        
        layout.addWidget(self.field_table)
        
        # 快速操作按钮
        button_layout = QHBoxLayout()
        
        auto_detect_btn = QPushButton("智能识别")
        auto_detect_btn.clicked.connect(self.auto_detect_field_types)
        button_layout.addWidget(auto_detect_btn)
        
        reset_btn = QPushButton("重置")
        reset_btn.clicked.connect(self.reset_field_types)
        button_layout.addWidget(reset_btn)
        
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        widget.setLayout(layout)
        return widget
    
    def create_format_rules_tab(self) -> QWidget:
        """创建格式化规则标签页（动态版本）"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # 说明文本
        info_label = QLabel("为不同类型的字段设置格式化规则。可以添加、编辑或删除规则。")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        self.add_rule_btn = QPushButton("➕ 添加规则")
        self.add_rule_btn.clicked.connect(self.add_formatting_rule)
        toolbar_layout.addWidget(self.add_rule_btn)
        
        self.apply_template_btn = QPushButton("📋 应用模板")
        self.apply_template_btn.clicked.connect(self.apply_rule_template)
        toolbar_layout.addWidget(self.apply_template_btn)
        
        self.clear_rules_btn = QPushButton("🗑️ 清空规则")
        self.clear_rules_btn.clicked.connect(self.clear_formatting_rules)
        toolbar_layout.addWidget(self.clear_rules_btn)
        
        toolbar_layout.addStretch()
        layout.addLayout(toolbar_layout)
        
        # 规则列表（使用表格）
        self.rules_table = QTableWidget()
        self.rules_table.setColumnCount(4)
        self.rules_table.setHorizontalHeaderLabels(["字段类型", "规则名称", "规则值", "操作"])
        self.rules_table.horizontalHeader().setStretchLastSection(False)
        self.rules_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.rules_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.rules_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)
        self.rules_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.Fixed)
        self.rules_table.setColumnWidth(3, 100)
        
        layout.addWidget(self.rules_table)
        
        # 初始化默认规则
        self.init_default_formatting_rules()
        
        widget.setLayout(layout)
        return widget
    
    def create_preview_tab(self) -> QWidget:
        """创建预览标签页"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # 分割器
        splitter = QSplitter(Qt.Vertical)
        
        # 原始数据预览
        original_group = QGroupBox("原始数据")
        original_layout = QVBoxLayout()
        
        self.original_table = QTableWidget()
        self.original_table.setMaximumHeight(250)
        original_layout.addWidget(self.original_table)
        
        original_group.setLayout(original_layout)
        splitter.addWidget(original_group)
        
        # 格式化后数据预览
        formatted_group = QGroupBox("格式化后数据")
        formatted_layout = QVBoxLayout()
        
        self.formatted_table = QTableWidget()
        self.formatted_table.setMaximumHeight(250)
        formatted_layout.addWidget(self.formatted_table)
        
        formatted_group.setLayout(formatted_layout)
        splitter.addWidget(formatted_group)
        
        layout.addWidget(splitter)
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新预览")
        refresh_btn.clicked.connect(self.refresh_preview)
        layout.addWidget(refresh_btn)
        
        widget.setLayout(layout)
        return widget
    
    def load_excel_preview(self):
        """加载Excel数据预览"""
        if self.excel_data is None or self.excel_data.empty:
            return
        
        # 获取列名
        columns = list(self.excel_data.columns)
        
        # 填充字段配置表格
        self.field_table.setRowCount(len(columns))
        
        # 🔧 [P1修复] 确保每行有足够高度显示下拉框
        for row in range(len(columns)):
            self.field_table.setRowHeight(row, 50)  # 适中的行高
        
        for i, col in enumerate(columns):
            # Excel字段名
            self.field_table.setItem(i, 0, QTableWidgetItem(col))
            
            # 字段类型下拉框（使用新的创建方法）
            type_combo = self.create_field_type_combo()
            
            # 智能推断并设置默认字段类型
            if col in self.excel_data.columns:
                inferred_type = self._infer_field_type(col, self.excel_data[col])
                if not self._set_combo_by_data(type_combo, inferred_type):
                    # 如果推断的类型不在列表中，默认选择文本类型
                    self._set_combo_by_data(type_combo, "text_string")
            else:
                # 如果没有数据，默认选择文本类型
                self._set_combo_by_data(type_combo, "text_string")
            
            # 🔧 [P0修复] 创建一个容器widget来实现垂直居中，并添加错误检查
            container = QWidget()
            container.setObjectName(f"combo_container_{i}")  # 设置对象名便于调试
            layout = QVBoxLayout(container)
            layout.setContentsMargins(2, 0, 2, 0)  # 减小上边距，保持下边距防止遮挡
            layout.setAlignment(Qt.AlignVCenter)  # 垂直居中对齐
            layout.setSpacing(0)  # 无额外间距

            # 🔧 [P0修复] 确保下拉框正确创建
            if not isinstance(type_combo, QComboBox):
                logger.error(f"第{i}行创建的type_combo不是QComboBox类型: {type(type_combo)}")
                # 创建一个简单的QComboBox作为备用
                type_combo = QComboBox()
                type_combo.addItem("文本字符串", "text_string")
                type_combo.setMinimumHeight(28)

            layout.addWidget(type_combo)

            # 🔧 [P0修复] 验证容器创建是否成功
            if not container.layout() or container.layout().count() == 0:
                logger.error(f"第{i}行容器创建失败")
                # 直接使用下拉框，不使用容器
                self.field_table.setCellWidget(i, 1, type_combo)
            else:
                self.field_table.setCellWidget(i, 1, container)
            
            # 数据库字段名（默认与Excel字段相同）
            self.field_table.setItem(i, 2, QTableWidgetItem(col))
            
            # 示例数据
            sample_data = ""
            non_null_data = self.excel_data[col].dropna()
            if len(non_null_data) > 0:
                sample_data = str(non_null_data.iloc[0])
            self.field_table.setItem(i, 3, QTableWidgetItem(sample_data))
        
        # 加载原始数据预览
        self.load_data_preview(self.original_table, self.excel_data.head(10))
    
    def load_data_preview(self, table_widget: QTableWidget, data: pd.DataFrame):
        """加载数据到表格控件"""
        if data is None or data.empty:
            return
        
        # 设置行列数
        table_widget.setRowCount(len(data))
        table_widget.setColumnCount(len(data.columns))
        table_widget.setHorizontalHeaderLabels(list(data.columns))
        
        # 填充数据
        for i in range(len(data)):
            for j in range(len(data.columns)):
                value = data.iloc[i, j]
                if pd.isna(value):
                    value = ""
                else:
                    value = str(value)
                table_widget.setItem(i, j, QTableWidgetItem(value))
        
        # 调整列宽
        table_widget.resizeColumnsToContents()
    
    def auto_detect_field_types(self):
        """智能识别字段类型"""
        if self.excel_data is None:
            QMessageBox.warning(self, "警告", "没有可分析的数据")
            return
        
        logger.info("开始智能识别字段类型")
        
        for i in range(self.field_table.rowCount()):
            col_name = self.field_table.item(i, 0).text()
            widget = self.field_table.cellWidget(i, 1)
            
            if col_name not in self.excel_data.columns:
                continue
            
            # 从容器中获取真正的下拉框
            type_combo = None
            if widget:
                if isinstance(widget, QWidget) and widget.layout():
                    for j in range(widget.layout().count()):
                        item = widget.layout().itemAt(j).widget()
                        if isinstance(item, QComboBox):
                            type_combo = item
                            break
                elif isinstance(widget, QComboBox):
                    type_combo = widget
            
            if type_combo:
                # 智能推断字段类型
                field_type = self._infer_field_type(col_name, self.excel_data[col_name])
                # 使用辅助方法根据userData设置
                self._set_combo_by_data(type_combo, field_type)
        
        logger.info("字段类型识别完成")
        QMessageBox.information(self, "提示", "字段类型识别完成")
    
    def _infer_field_type(self, column_name: str, series: pd.Series) -> str:
        """
        推断字段类型
        
        Args:
            column_name: 列名
            series: 数据序列
            
        Returns:
            字段类型
        """
        # 基于字段名的规则
        name_lower = column_name.lower()
        
        # 工号相关
        if any(k in column_name for k in ['工号', '员工号', '职工号', 'employee_id', 'staff_id']):
            return 'employee_id_string'
        
        # 姓名相关
        if any(k in column_name for k in ['姓名', '名字', 'name', '员工姓名']):
            return 'name_string'
        
        # 工资相关
        if any(k in column_name for k in ['工资', '薪', '津贴', '补贴', '奖金', '绩效', '扣款', '实发']):
            return 'salary_float'
        
        # 日期相关
        if any(k in column_name for k in ['日期', '时间', 'date', 'time', '年', '月']):
            return 'date_string'
        
        # 代码相关
        if any(k in column_name for k in ['代码', 'code', '编码']):
            return 'code_string'
        
        # 身份证
        if any(k in column_name for k in ['身份证', 'id_card', 'id_number']):
            return 'id_number_string'
        
        # 基于数据特征推断
        non_null = series.dropna()
        if len(non_null) > 0:
            # 获取一些样本数据进行分析
            samples = non_null.head(10).astype(str)
            
            # 检查是否为日期格式
            try:
                date_parsed = pd.to_datetime(samples, errors='coerce')
                if date_parsed.notna().sum() / len(samples) > 0.8:
                    return 'date_string'
            except:
                pass
            
            # 检查是否为身份证号（18位数字）
            if samples.str.match(r'^\d{17}[\dXx]$').sum() / len(samples) > 0.8:
                return 'id_number_string'
            
            # 检查是否为工号格式（字母数字组合，通常较短）
            if samples.str.match(r'^[A-Za-z0-9]{3,20}$').sum() / len(samples) > 0.8:
                avg_len = samples.str.len().mean()
                if avg_len <= 10:
                    # 短的字母数字组合，可能是工号或代码
                    if any(k in column_name.lower() for k in ['工号', '编号', 'id', 'no']):
                        return 'employee_id_string'
                    else:
                        return 'code_string'
            
            # 尝试转换为数值
            try:
                numeric_values = pd.to_numeric(non_null, errors='coerce')
                valid_ratio = numeric_values.notna().sum() / len(non_null)
                
                if valid_ratio > 0.8:  # 80%以上可以转换为数字
                    # 检查数值范围，判断是否为金额
                    if numeric_values.max() > 1000 and any(k in column_name for k in ['金额', '工资', '薪', '费']):
                        return 'salary_float'
                    
                    # 检查是否都是整数
                    is_integer = all(numeric_values.dropna() == numeric_values.dropna().astype(int))
                    if is_integer:
                        # 检查是否为年份或月份
                        if numeric_values.min() >= 1900 and numeric_values.max() <= 2100:
                            return 'integer'  # 可能是年份
                        elif numeric_values.min() >= 1 and numeric_values.max() <= 12:
                            return 'integer'  # 可能是月份
                        else:
                            return 'integer'
                    else:
                        # 浮点数，可能是金额或比率
                        if numeric_values.max() > 100:
                            return 'salary_float'  # 较大的数字，可能是金额
                        else:
                            return 'float'  # 较小的数字，可能是比率等
            except:
                pass
            
            # 检查是否为中文姓名（2-4个汉字）
            if samples.str.match(r'^[\u4e00-\u9fa5]{2,4}$').sum() / len(samples) > 0.8:
                return 'name_string'
        
        # 默认为文本
        return 'text_string'
    
    def reset_field_types(self):
        """重置字段类型"""
        for i in range(self.field_table.rowCount()):
            widget = self.field_table.cellWidget(i, 1)
            
            # 从容器中获取真正的下拉框
            type_combo = None
            if widget:
                if isinstance(widget, QWidget) and widget.layout():
                    for j in range(widget.layout().count()):
                        item = widget.layout().itemAt(j).widget()
                        if isinstance(item, QComboBox):
                            type_combo = item
                            break
                elif isinstance(widget, QComboBox):
                    type_combo = widget
            
            if type_combo:
                # 查找text_string对应的索引
                for idx in range(type_combo.count()):
                    if type_combo.itemData(idx) == "text_string":
                        type_combo.setCurrentIndex(idx)
                        break
        
        logger.info("字段类型已重置")
    
    def show_formatting_warning(self, error_fields: List[str]):
        """显示格式化警告
        
        Args:
            error_fields: 格式化失败的字段列表
        """
        if len(error_fields) <= 3:
            field_list = "、".join(error_fields)
        else:
            field_list = f"{', '.join(error_fields[:3])} 等{len(error_fields)}个字段"
        
        msg = f"以下字段格式化时遇到问题：{field_list}\n\n"
        msg += "已使用原始数据显示。建议检查字段类型设置是否正确。"
        
        # 使用信息提示而不是警告，避免过度打扰用户
        logger.info(f"格式化警告：{error_fields}")
        # 可以选择在状态栏显示，而不是弹窗
        # 如果有状态栏的话：self.status_bar.showMessage(f"部分字段格式化失败，已使用原始数据", 3000)
    
    def refresh_preview(self):
        """刷新预览"""
        if self.excel_data is None or self.excel_data.empty:
            QMessageBox.warning(self, "警告", "没有可预览的数据")
            return
        
        # 获取当前配置
        current_config = self.get_current_configuration()
        
        # 应用格式化规则（这里只是模拟，实际格式化在导入时进行）
        formatted_data = self.excel_data.head(10).copy()
        
        # 根据字段类型简单处理
        for i in range(self.field_table.rowCount()):
            col_name = self.field_table.item(i, 0).text()
            widget = self.field_table.cellWidget(i, 1)
            
            if not widget or col_name not in formatted_data.columns:
                continue
            
            # 从容器或直接获取下拉框
            type_combo = None
            if isinstance(widget, QWidget) and widget.layout():
                # 从容器布局中获取下拉框
                for j in range(widget.layout().count()):
                    item = widget.layout().itemAt(j).widget()
                    if isinstance(item, QComboBox):
                        type_combo = item
                        break
            elif isinstance(widget, QComboBox):
                # 直接是下拉框
                type_combo = widget
            
            if not type_combo:
                continue
            
            # 从下拉框获取字段类型（可能包含显示文本）
            field_type_text = type_combo.currentText()
            # 提取实际的字段类型key
            field_type = type_combo.currentData() if type_combo.currentData() else field_type_text
            
            # 应用不同类型的格式化
            try:
                if field_type == "salary_float" or (field_type_text and "工资" in field_type_text):
                    # 🔧 [用户需求修复] 金额格式化：根据用户设置的格式化规则
                    formatted_data[col_name] = formatted_data[col_name].apply(
                        lambda x: self._format_salary_value(x, field_type, current_config) if pd.notna(x) and str(x).strip() else ""
                    )
                    
                elif field_type == "employee_id_string" or (field_type_text and "工号" in field_type_text):
                    # 工号格式化：去除小数点，保持原始字符串
                    formatted_data[col_name] = formatted_data[col_name].apply(
                        lambda x: str(int(float(x))) if pd.notna(x) and str(x).strip() else ""
                    )
                    
                elif field_type == "name_string" or (field_type_text and "姓名" in field_type_text):
                    # 姓名格式化：去除多余空格
                    formatted_data[col_name] = formatted_data[col_name].apply(
                        lambda x: str(x).strip() if pd.notna(x) else ""
                    )
                    
                elif field_type == "date_string" or (field_type_text and "日期" in field_type_text):
                    # 日期格式化：统一格式为 YYYY-MM-DD
                    formatted_data[col_name] = formatted_data[col_name].apply(
                        lambda x: pd.to_datetime(x).strftime("%Y-%m-%d") if pd.notna(x) else ""
                    )
                    
                elif field_type == "id_number_string" or (field_type_text and "身份证" in field_type_text):
                    # 身份证格式化：确保18位，隐藏中间部分
                    formatted_data[col_name] = formatted_data[col_name].apply(
                        lambda x: str(x)[:6] + "********" + str(x)[-4:] if pd.notna(x) and len(str(x)) >= 18 else str(x) if pd.notna(x) else ""
                    )
                    
                elif field_type == "personnel_category_code" or (field_type_text and "人员类别代码" in field_type_text):
                    # 人员类别代码格式化：单位数前补0，最少2位
                    formatted_data[col_name] = formatted_data[col_name].apply(
                        lambda x: str(int(float(x))).zfill(2) if pd.notna(x) and str(x).strip() else ""
                    )
                    
                elif field_type == "code_string" or (field_type_text and "代码" in field_type_text):
                    # 代码格式化：保持原样，去除空格
                    formatted_data[col_name] = formatted_data[col_name].apply(
                        lambda x: str(x).strip() if pd.notna(x) else ""
                    )
                    
                elif field_type == "float" or (field_type_text and "浮点" in field_type_text):
                    # 浮点数格式化：保留4位小数
                    formatted_data[col_name] = formatted_data[col_name].apply(
                        lambda x: f"{float(x):.4f}" if pd.notna(x) and str(x).strip() else ""
                    )
                    
                elif field_type == "integer" or (field_type_text and "整数" in field_type_text):
                    # 整数格式化：转为整数显示
                    formatted_data[col_name] = formatted_data[col_name].apply(
                        lambda x: str(int(float(x))) if pd.notna(x) and str(x).strip() else ""
                    )
                    
                else:
                    # 默认文本格式化：去除前后空格
                    formatted_data[col_name] = formatted_data[col_name].apply(
                        lambda x: str(x).strip() if pd.notna(x) else ""
                    )
            except Exception as e:
                logger.warning(f"格式化字段 {col_name} 失败: {e}")
                # 错误恢复：使用原始数据
                formatted_data[col_name] = self.excel_data[col_name].apply(
                    lambda x: str(x) if pd.notna(x) else ""
                )
                # 记录错误字段，用于后续用户提示
                if not hasattr(self, '_formatting_errors'):
                    self._formatting_errors = []
                self._formatting_errors.append({
                    'field': col_name,
                    'type': field_type,
                    'error': str(e)
                })
        
        # 加载格式化后的数据
        self.load_data_preview(self.formatted_table, formatted_data)
        
        # 如果有格式化错误，提示用户
        if hasattr(self, '_formatting_errors') and self._formatting_errors:
            error_fields = [err['field'] for err in self._formatting_errors]
            self.show_formatting_warning(error_fields)
            # 清空错误列表，避免重复提示
            self._formatting_errors = []
        
        logger.info("预览已刷新")

    def _format_salary_value(self, value, field_type: str, config: Dict[str, Any]) -> str:
        """根据用户设置的格式化规则格式化工资数值

        🔧 [用户需求修复] 根据格式化规则表中的设置来格式化数据，而不是硬编码

        Args:
            value: 原始数值
            field_type: 字段类型
            config: 当前配置（包含formatting_rules）

        Returns:
            str: 格式化后的字符串
        """
        try:
            # 转换为浮点数
            float_value = float(value)

            # 获取格式化规则
            formatting_rules = config.get("formatting_rules", {})
            salary_rules = formatting_rules.get(field_type, {})

            # 获取小数位数（默认2位）
            decimal_places = int(salary_rules.get("小数位数", "2"))

            # 🔧 [用户需求修复] 获取千位分隔符设置（删除规则时默认"否"）
            use_thousands_separator = salary_rules.get("千位分隔符", "否") == "是"

            # 获取负数格式（默认"负号"）
            negative_format = salary_rules.get("负数格式", "负号")

            # 格式化数值
            if use_thousands_separator:
                # 使用千位分隔符
                formatted = f"{float_value:,.{decimal_places}f}"
            else:
                # 不使用千位分隔符
                formatted = f"{float_value:.{decimal_places}f}"

            # 处理负数格式
            if float_value < 0:
                if negative_format == "括号":
                    formatted = f"({formatted.replace('-', '')})"
                elif negative_format == "红色":
                    # 这里只是标记，实际颜色需要在UI层处理
                    formatted = f"[红色]{formatted}[/红色]"
                # 默认使用负号，不需要额外处理

            return formatted

        except (ValueError, TypeError) as e:
            logger.warning(f"格式化工资数值失败: {value} -> {e}")
            return str(value) if value is not None else ""

    def get_current_configuration(self) -> Dict[str, Any]:
        """获取当前配置"""
        config = {
            "field_mapping": {},
            "field_types": {},
            "formatting_rules": {}
        }
        
        # 获取字段配置
        for i in range(self.field_table.rowCount()):
            excel_field = self.field_table.item(i, 0).text()
            widget = self.field_table.cellWidget(i, 1)
            db_field = self.field_table.item(i, 2).text()
            
            # 从容器或直接获取下拉框
            type_combo = None
            if widget:
                if isinstance(widget, QWidget) and widget.layout():
                    # 从容器布局中获取下拉框
                    for j in range(widget.layout().count()):
                        item = widget.layout().itemAt(j).widget()
                        if isinstance(item, QComboBox):
                            type_combo = item
                            break
                elif isinstance(widget, QComboBox):
                    # 直接是下拉框
                    type_combo = widget
            
            if type_combo:
                config["field_mapping"][excel_field] = db_field
                # 保存字段类型的数据值（key），而不是显示文本
                field_type = type_combo.currentData()
                if not field_type:  # 如果没有data，fallback到文本
                    field_type = type_combo.currentText()
                config["field_types"][excel_field] = field_type
        
        # 从动态规则表获取格式化规则
        config["formatting_rules"] = self.get_formatting_rules()
        
        return config
    
    def _sanitize_filename(self, filename: str) -> str:
        """清理文件名，移除特殊字符"""
        import re
        # 移除不安全的字符
        sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename.strip())
        # 移除多余的下划线
        sanitized = re.sub(r'_+', '_', sanitized)
        # 移除首尾下划线
        sanitized = sanitized.strip('_')
        return sanitized if sanitized else 'unnamed_config'
    
    def _collect_all_configured_sheets(self) -> Dict[str, Dict[str, Any]]:
        """收集所有工作表配置（包括未修改的表）

        🔧 [用户需求修复] 保存所有工作表的配置，不管是否有修改
        为没有配置的工作表生成默认配置，确保配置文件完整

        Returns:
            Dict: {工作表名: 配置字典}
        """
        logger.info("🔍 开始收集所有工作表的配置状态（包括未修改的表）...")
        configs_to_save = {}

        try:
            # 🔧 关键修复：先保存当前工作表配置到all_sheets_configs
            self._save_current_sheet_config_to_cache()

            # 1. 首先保存当前工作表的配置（确保不遗漏）
            # 🔧 修复：如果current_sheet_name是unknown，尝试获取真实的工作表名称
            actual_sheet_name = self.current_sheet_name
            if self.current_sheet_name == 'unknown' and self.all_sheets_data:
                # 如果是unknown但有工作表数据，使用第一个工作表的名称
                actual_sheet_name = list(self.all_sheets_data.keys())[0]
                logger.info(f"🔧 修复工作表名称：从 'unknown' 更正为 '{actual_sheet_name}'")

            if hasattr(self, 'current_sheet_name') and actual_sheet_name:
                try:
                    current_config = self.get_current_configuration()
                    if current_config and current_config.get('field_mapping'):
                        configs_to_save[actual_sheet_name] = current_config
                        logger.info(f"✅ 收集到当前工作表 '{actual_sheet_name}' 配置：{len(current_config.get('field_mapping', {}))} 个字段")
                except Exception as e:
                    logger.warning(f"获取当前工作表配置时出错: {e}")

            # 2. 从all_sheets_configs中收集其他已配置的工作表
            for sheet_name, config in self.all_sheets_configs.items():
                if config and config.get('field_mapping'):
                    if sheet_name not in configs_to_save:  # 避免重复
                        configs_to_save[sheet_name] = config
                        logger.info(f"✅ 收集到工作表 '{sheet_name}' 配置：{len(config.get('field_mapping', {}))} 个字段")

            # 3. 从父窗口的change_data_configs中收集配置（如果存在）
            try:
                parent = self.parent()
                if parent and hasattr(parent, 'change_data_configs') and parent.change_data_configs:
                    for sheet_name, config in parent.change_data_configs.items():
                        if config and config.get('field_mapping'):
                            if sheet_name not in configs_to_save:  # 避免重复
                                configs_to_save[sheet_name] = config
                                logger.info(f"✅ 从父窗口收集到工作表 '{sheet_name}' 配置：{len(config.get('field_mapping', {}))} 个字段")
            except Exception as e:
                logger.warning(f"从父窗口获取配置时出错: {e}")

            # 🔧 [用户需求修复] 4. 为所有工作表生成配置（包括未修改的表）
            if hasattr(self, 'all_sheets_data') and self.all_sheets_data:
                for sheet_name, sheet_data in self.all_sheets_data.items():
                    if sheet_name not in configs_to_save:
                        # 为未配置的工作表生成默认配置
                        default_config = self._generate_default_config_for_sheet(sheet_name, sheet_data)
                        if default_config:
                            configs_to_save[sheet_name] = default_config
                            logger.info(f"🆕 为未配置工作表 '{sheet_name}' 生成默认配置：{len(default_config.get('field_mapping', {}))} 个字段")

            # 5. 输出收集结果
            if configs_to_save:
                logger.info(f"🎯 配置收集完成，共收集 {len(configs_to_save)} 个工作表的配置：")
                for sheet_name in configs_to_save.keys():
                    field_count = len(configs_to_save[sheet_name].get('field_mapping', {}))
                    logger.info(f"   - {sheet_name}: {field_count} 个字段")
            else:
                logger.warning("⚠️ 没有找到任何工作表数据")

        except Exception as e:
            logger.error(f"收集工作表配置时发生错误: {e}")

        return configs_to_save

    def _generate_default_config_for_sheet(self, sheet_name: str, sheet_data: pd.DataFrame) -> Dict[str, Any]:
        """为工作表生成默认配置

        🔧 [用户需求修复] 为未配置的工作表生成默认配置，确保所有表都有配置信息

        Args:
            sheet_name: 工作表名称
            sheet_data: 工作表数据

        Returns:
            Dict: 默认配置字典
        """
        try:
            if sheet_data is None or sheet_data.empty:
                logger.warning(f"工作表 '{sheet_name}' 数据为空，无法生成默认配置")
                return None

            # 获取列名
            columns = list(sheet_data.columns)

            # 生成默认字段映射（字段名映射到自己）
            field_mapping = {}
            field_types = {}

            for col in columns:
                col_str = str(col).strip()
                if col_str:  # 跳过空列名
                    field_mapping[col_str] = col_str
                    # 根据列名推断字段类型
                    field_types[col_str] = self._infer_field_type_from_name(col_str)

            # 生成默认格式化规则
            formatting_rules = {
                "salary_float": {
                    "小数位数": "2",
                    "千位分隔符": "是",
                    "负数格式": "负号"
                },
                "employee_id_string": {
                    "保留前导零": "是",
                    "最小长度": "6"
                },
                "date_string": {
                    "日期格式": "%Y-%m-%d"
                }
            }

            default_config = {
                "field_mapping": field_mapping,
                "field_types": field_types,
                "formatting_rules": formatting_rules
            }

            logger.info(f"为工作表 '{sheet_name}' 生成默认配置：{len(field_mapping)} 个字段")
            return default_config

        except Exception as e:
            logger.error(f"为工作表 '{sheet_name}' 生成默认配置失败: {e}")
            return None

    def _infer_field_type_from_name(self, field_name: str) -> str:
        """根据字段名推断字段类型

        Args:
            field_name: 字段名称

        Returns:
            str: 推断的字段类型
        """
        field_name_lower = field_name.lower()

        # 工号相关
        if any(keyword in field_name for keyword in ['工号', '人员代码', '员工编号']):
            return 'employee_id_string'

        # 姓名相关
        if any(keyword in field_name for keyword in ['姓名', '员工姓名', '人员姓名']):
            return 'name_string'

        # 人员类别代码
        if any(keyword in field_name for keyword in ['人员类别代码', '类别代码']):
            return 'personnel_category_code'

        # 工资金额相关
        if any(keyword in field_name for keyword in ['工资', '津贴', '补贴', '奖金', '绩效', '公积金', '保险', '扣款', '应发', '实发', '合计']):
            return 'salary_float'

        # 日期相关
        if any(keyword in field_name for keyword in ['日期', '时间', '年月']):
            return 'date_string'

        # 序号
        if field_name in ['序号', '编号', 'No', 'ID']:
            return 'integer'

        # 默认为文本
        return 'text_string'

    def save_configuration(self):
        """另存配置：一次性保存所有已配置的工作表"""
        try:
            # 🔧 重新设计：扫描所有工作表的实际配置状态
            configs_to_save = self._collect_all_configured_sheets()
            
            # 检查是否有配置需要保存
            if not configs_to_save:
                QMessageBox.warning(self, "警告", "没有找到任何已配置的工作表\n请先配置至少一个工作表的字段类型")
                return
            
            # 获取配置名称
            config_name, ok = self.get_config_name_dialog()
            if not ok or not config_name:
                return
            
            if not config_name.strip():
                QMessageBox.warning(self, "警告", "配置名称不能为空")
                return
            
            # 清理配置名称
            clean_name = self._sanitize_filename(config_name)
            
            # 创建用户配置目录
            user_config_dir = self.config_manager.config_dir / 'user_configs'
            user_config_dir.mkdir(exist_ok=True)
            
            # 检查是否覆盖现有文件
            config_file_path = user_config_dir / f"{clean_name}.json"
            if config_file_path.exists():
                reply = QMessageBox.question(
                    self, "文件已存在", 
                    f"配置文件 '{clean_name}.json' 已存在。\n是否覆盖？",
                    QMessageBox.Yes | QMessageBox.No
                )
                if reply != QMessageBox.Yes:
                    return
            
            # 统计总字段数
            total_fields = sum(len(config.get('field_mapping', {})) for config in configs_to_save.values())
            
            # 创建多表配置结构
            from datetime import datetime
            multi_sheet_config = {
                'name': clean_name,
                'description': f"多工作表配置 - {len(configs_to_save)} 个工作表，共 {total_fields} 个字段",
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat(),
                'version': '2.0',  # 多表配置版本
                'type': 'multi_sheet_user_config',
                'sheet_count': len(configs_to_save),
                'sheets': {}
            }
            
            # 保存每个工作表的配置
            for sheet_name, sheet_config in configs_to_save.items():
                multi_sheet_config['sheets'][sheet_name] = {
                    'sheet_name': sheet_name,
                    'field_count': len(sheet_config.get('field_mapping', {})),
                    'config': sheet_config
                }
            
            # 🔧 关键修复：保存配置后，更新all_sheets_configs缓存
            # 这确保下次操作时能找到之前配置的工作表
            for sheet_name, sheet_config in configs_to_save.items():
                self.all_sheets_configs[sheet_name] = sheet_config
                logger.info(f"🔧 [缓存更新] 工作表 '{sheet_name}' 配置已更新到缓存")
            
            # 保存到独立文件
            with open(config_file_path, 'w', encoding='utf-8') as f:
                json.dump(multi_sheet_config, f, ensure_ascii=False, indent=2)
            
            # 显示详细的保存信息
            sheet_info = "\n".join([
                f"• {sheet_name}: {data['field_count']} 个字段" 
                for sheet_name, data in multi_sheet_config['sheets'].items()
            ])
            
            QMessageBox.information(
                self, "另存成功", 
                f"配置已另存为：{config_file_path.name}\n"
                f"保存位置：{config_file_path.parent}\n"
                f"包含工作表：\n{sheet_info}\n"
                f"总字段数：{total_fields}"
            )
            
            logger.info(f"多工作表配置另存成功: {config_file_path}, 包含 {len(configs_to_save)} 个工作表")
            
        except Exception as e:
            logger.error(f"另存配置时发生错误: {e}")
            QMessageBox.critical(self, "错误", f"另存配置时发生错误：\n{str(e)}")
    
    def get_config_name_dialog(self) -> tuple:
        """获取配置名称对话框"""
        from PyQt5.QtWidgets import QInputDialog
        
        text, ok = QInputDialog.getText(
            self, 
            "另存配置",
            "请输入配置名称（将保存所有已配置的工作表）:",
            text=""
        )
        
        return text, ok
    
    def apply_configuration(self):
        """应用配置并关闭对话框"""
        config = self.get_current_configuration()
        self.config_saved.emit(config)
        self.accept()
    
    def load_selected_config(self, config_name: str):
        """加载选中的配置"""
        try:
            if not config_name or config_name.startswith("-- "):
                return
            
            # 获取实际的配置名（从userData中）
            current_index = self.config_combo.currentIndex()
            if current_index > 0:  # 跳过第一个提示项
                actual_config_name = self.config_combo.itemData(current_index)
                if actual_config_name:
                    config_name = actual_config_name
            
            # 处理用户配置和系统配置的区分
            config = None
            if config_name.startswith("user:"):
                # 用户配置
                user_config_name = config_name[5:]  # 移除 "user:" 前缀
                config = self.config_manager.load_user_config(user_config_name)
                logger.info(f"尝试加载用户配置: {user_config_name}")
            elif config_name.startswith("system:"):
                # 系统配置
                system_config_name = config_name[7:]  # 移除 "system:" 前缀
                config = self._load_system_config_with_sheet_selection(system_config_name)
                logger.info(f"尝试加载系统配置: {system_config_name}")
            else:
                # 兼容旧版本，直接加载
                config = self.config_manager.load_config(config_name)
                logger.info(f"尝试加载配置（兼容模式）: {config_name}")
            
            if config:
                # 验证配置与当前数据的兼容性
                if self.excel_data is not None:
                    metadata = config.get('metadata', {})
                    saved_columns = metadata.get('excel_columns', [])
                    current_columns = list(self.excel_data.columns)
                    
                    # 检查列是否匹配
                    if saved_columns and saved_columns != current_columns:
                        missing_cols = set(saved_columns) - set(current_columns)
                        extra_cols = set(current_columns) - set(saved_columns)
                        
                        msg = "配置的字段与当前数据不完全匹配：\n"
                        if missing_cols:
                            msg += f"缺少字段：{', '.join(missing_cols)}\n"
                        if extra_cols:
                            msg += f"额外字段：{', '.join(extra_cols)}\n"
                        msg += "\n是否仍要应用此配置？"
                        
                        reply = QMessageBox.question(self, "字段不匹配", msg,
                                                    QMessageBox.Yes | QMessageBox.No)
                        if reply != QMessageBox.Yes:
                            return
                
                self.apply_config_to_ui(config)
                logger.info(f"已加载配置: {config_name}")
                QMessageBox.information(self, "成功", f"已成功加载配置：{config_name}")
            else:
                QMessageBox.warning(self, "警告", f"无法加载配置：{config_name}")
                logger.warning(f"配置加载失败: {config_name}")
        except Exception as e:
            logger.error(f"加载配置时发生错误: {e}")
            QMessageBox.critical(self, "错误", f"加载配置时发生错误：\n{str(e)}")
    
    def _set_combo_by_data(self, combo: QComboBox, data_value: str) -> bool:
        """
        根据userData设置下拉框的当前选项

        Args:
            combo: 下拉框
            data_value: 要设置的userData值

        Returns:
            是否设置成功
        """
        try:
            # 🔧 [修复] 添加类型检查和错误处理
            if not isinstance(combo, QComboBox):
                logger.error(f"传入的对象不是QComboBox，而是 {type(combo)}")
                return False

            for i in range(combo.count()):
                if combo.itemData(i) == data_value:
                    combo.setCurrentIndex(i)
                    return True

            # 如果没有找到匹配的userData，尝试按文本匹配
            for i in range(combo.count()):
                if combo.itemText(i) == data_value:
                    combo.setCurrentIndex(i)
                    logger.info(f"通过文本匹配设置下拉框: {data_value}")
                    return True

            logger.warning(f"未找到匹配的选项: {data_value}")
            return False

        except Exception as e:
            logger.error(f"设置下拉框时发生错误: {e}")
            return False

    def _get_combo_from_container(self, container_widget) -> Optional[QComboBox]:
        """🔧 [P0修复] 从容器widget中获取QComboBox，增强错误检查"""
        if not container_widget:
            logger.warning("容器widget为空")
            return None

        try:
            # 如果直接就是QComboBox
            if isinstance(container_widget, QComboBox):
                return container_widget

            # 🔧 [P0修复] 验证容器widget类型
            if not isinstance(container_widget, QWidget):
                logger.warning(f"容器widget不是QWidget类型: {type(container_widget)}")
                return None

            # 如果是容器widget，查找其中的QComboBox
            if hasattr(container_widget, 'findChild'):
                combo = container_widget.findChild(QComboBox)
                if combo and isinstance(combo, QComboBox):
                    return combo

            # 如果有layout，遍历layout中的widget
            if hasattr(container_widget, 'layout') and container_widget.layout():
                layout = container_widget.layout()
                if layout:
                    for i in range(layout.count()):
                        item = layout.itemAt(i)
                        if item and item.widget():
                            widget = item.widget()
                            if isinstance(widget, QComboBox):
                                return widget
                else:
                    logger.warning("容器widget的layout为空")

            logger.warning(f"在容器widget中未找到QComboBox: {container_widget.objectName()}")
            return None

        except Exception as e:
            logger.error(f"从容器获取QComboBox时发生异常: {e}")
            return None

    def apply_config_to_ui(self, config: Dict[str, Any]):
        """将配置应用到界面"""
        field_mapping = config.get("field_mapping", {})
        field_types = config.get("field_types", {})
        formatting_rules = config.get("formatting_rules", {})
        
        # 应用字段配置
        for i in range(self.field_table.rowCount()):
            excel_field = self.field_table.item(i, 0).text()
            
            if excel_field in field_mapping:
                # 设置数据库字段
                self.field_table.setItem(i, 2, QTableWidgetItem(field_mapping[excel_field]))
            
            if excel_field in field_types:
                # 设置字段类型
                # 🔧 [修复] 获取容器中的QComboBox
                container_widget = self.field_table.cellWidget(i, 1)
                type_combo = self._get_combo_from_container(container_widget)

                if type_combo and isinstance(type_combo, QComboBox):
                    # 使用新的辅助方法根据userData设置
                    success = self._set_combo_by_data(type_combo, field_types[excel_field])
                    if success:
                        logger.info(f"成功设置字段 '{excel_field}' 的类型为: {field_types[excel_field]}")
                    else:
                        logger.warning(f"无法设置字段 '{excel_field}' 的类型: {field_types[excel_field]}")
                elif container_widget:
                    # 如果不是QComboBox，记录警告信息
                    logger.warning(f"字段 '{excel_field}' 的类型控件不是QComboBox，容器类型: {type(container_widget)}, 内容: {type(type_combo) if type_combo else 'None'}")
        
        # 应用格式化规则到动态规则表
        self.apply_formatting_rules_to_table(formatting_rules)

        # 🔧 [修复] 应用配置后刷新数据预览
        self.refresh_preview()
        logger.info("配置已应用到界面，数据预览已刷新")
    
    def preview_template(self):
        """预览模板内容"""
        current_index = self.template_combo.currentIndex()
        if current_index <= 0:  # 忽略"-- 选择模板 --"选项
            QMessageBox.information(self, "提示", "请先选择一个模板")
            return
        
        # 获取模板key
        template_key = self.template_combo.currentData()
        if not template_key:
            return
        
        # 获取模板内容
        template = self.config_manager.get_template(template_key)
        if not template:
            QMessageBox.warning(self, "警告", "无法获取模板内容")
            return
        
        # 创建预览对话框
        preview_dialog = QDialog(self)
        preview_dialog.setWindowTitle(f"模板预览 - {template.get('name', template_key)}")
        preview_dialog.setMinimumSize(800, 600)
        
        layout = QVBoxLayout()
        
        # 模板描述
        desc_label = QLabel(f"<b>描述:</b> {template.get('description', '无描述')}")
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)
        
        # 创建标签页显示不同内容
        tab_widget = QTabWidget()
        
        # 字段类型标签页
        field_types_tab = QTableWidget()
        field_types = template.get('field_types', {})
        field_types_tab.setRowCount(len(field_types))
        field_types_tab.setColumnCount(2)
        field_types_tab.setHorizontalHeaderLabels(["字段名", "类型"])
        
        row = 0
        for field_name, field_type in field_types.items():
            field_types_tab.setItem(row, 0, QTableWidgetItem(field_name))
            field_types_tab.setItem(row, 1, QTableWidgetItem(field_type))
            row += 1
        
        field_types_tab.resizeColumnsToContents()
        tab_widget.addTab(field_types_tab, "字段类型映射")
        
        # 格式化规则标签页
        rules_tab = QTextEdit()
        rules_tab.setReadOnly(True)
        formatting_rules = template.get('formatting_rules', {})
        
        rules_text = ""
        for field_type, rules in formatting_rules.items():
            rules_text += f"<b>{field_type}:</b><br>"
            if isinstance(rules, dict):
                for rule_name, rule_value in rules.items():
                    rules_text += f"  • {rule_name}: {rule_value}<br>"
            else:
                rules_text += f"  • {rules}<br>"
            rules_text += "<br>"
        
        rules_tab.setHtml(rules_text if rules_text else "无格式化规则")
        tab_widget.addTab(rules_tab, "格式化规则")
        
        layout.addWidget(tab_widget)
        
        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(preview_dialog.accept)
        layout.addWidget(close_btn)
        
        preview_dialog.setLayout(layout)
        preview_dialog.exec_()
    
    def apply_template(self):
        """应用模板"""
        current_index = self.template_combo.currentIndex()
        if current_index <= 0:  # 忽略"-- 选择模板 --"选项
            return
        
        # 获取模板key（存储在userData中）
        template_key = self.template_combo.currentData()
        if not template_key:
            return
        
        # 获取Excel列名
        excel_headers = []
        for i in range(self.field_table.rowCount()):
            excel_headers.append(self.field_table.item(i, 0).text())
        
        # 基于模板创建配置
        config = self.config_manager.create_config_from_template(template_key, excel_headers)
        
        if config:
            self.apply_config_to_ui(config)
            template_name = self.template_combo.currentText()
            logger.info(f"已应用模板: {template_name} (key: {template_key})")
            QMessageBox.information(self, "提示", f"已应用模板 '{template_name}'")
    
    def import_configuration(self):
        """导入配置并直接应用"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "导入配置",
            "",
            "JSON Files (*.json)"
        )

        if file_path:
            try:
                # 直接读取配置文件
                import json
                with open(file_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)

                # 验证配置格式
                if not self._validate_config_format(config_data):
                    QMessageBox.critical(self, "错误", "配置文件格式不正确")
                    return

                # 提取配置内容（处理多种格式）
                config = self._extract_config_content(config_data)
                if not config:
                    QMessageBox.critical(self, "错误", "无法解析配置文件内容")
                    return

                # 验证配置与当前数据的兼容性
                if self.excel_data is not None:
                    if not self._check_config_compatibility(config):
                        return  # 用户选择不应用不兼容的配置

                # 直接应用配置到界面
                self.apply_config_to_ui(config)

                # 询问是否保存到系统配置库
                reply = QMessageBox.question(
                    self, "保存配置",
                    "配置已成功应用到界面。\n是否要将此配置保存到系统配置库以便将来使用？",
                    QMessageBox.Yes | QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    config_name, ok = self.get_config_name_dialog()
                    if ok and config_name:
                        success = self.config_manager.import_config(file_path, config_name)
                        if success:
                            self.update_config_list()
                            self.config_combo.setCurrentText(config_name)
                            QMessageBox.information(self, "成功", "配置已应用并保存到系统配置库")
                        else:
                            QMessageBox.warning(self, "警告", "配置已应用，但保存到系统配置库失败")
                    else:
                        QMessageBox.information(self, "成功", "配置已应用（未保存到系统配置库）")
                else:
                    QMessageBox.information(self, "成功", "配置已应用（未保存到系统配置库）")

            except Exception as e:
                logger.error(f"导入配置时发生错误: {e}")
                QMessageBox.critical(self, "错误", f"导入配置时发生错误：\n{str(e)}")

    def _validate_config_format(self, config_data: dict) -> bool:
        """验证配置文件格式"""
        try:
            # 检查是否是多表配置格式
            if 'type' in config_data and config_data['type'] == 'multi_sheet_user_config':
                return 'sheets' in config_data and isinstance(config_data['sheets'], dict)

            # 检查是否是单表配置格式
            if 'field_mapping' in config_data:
                return isinstance(config_data['field_mapping'], dict)

            # 检查是否是系统配置格式
            if 'data' in config_data and isinstance(config_data['data'], dict):
                return 'field_mapping' in config_data['data']

            return False
        except Exception:
            return False

    def _extract_config_content(self, config_data: dict) -> dict:
        """提取配置内容"""
        try:
            # 多表配置格式：选择第一个工作表的配置
            if 'type' in config_data and config_data['type'] == 'multi_sheet_user_config':
                sheets = config_data.get('sheets', {})
                if sheets:
                    first_sheet = next(iter(sheets.values()))
                    return first_sheet.get('config', {})

            # 单表配置格式：直接返回
            if 'field_mapping' in config_data:
                return config_data

            # 系统配置格式：提取data部分
            if 'data' in config_data:
                return config_data['data']

            return {}
        except Exception:
            return {}

    def _check_config_compatibility(self, config: dict) -> bool:
        """检查配置与当前数据的兼容性"""
        try:
            field_mapping = config.get('field_mapping', {})
            if not field_mapping:
                return True  # 空配置认为兼容

            current_columns = list(self.excel_data.columns)
            config_columns = list(field_mapping.keys())

            missing_cols = set(config_columns) - set(current_columns)
            extra_cols = set(current_columns) - set(config_columns)

            if missing_cols or extra_cols:
                msg = "配置的字段与当前数据不完全匹配：\n"
                if missing_cols:
                    msg += f"配置中有但数据中缺少的字段：{', '.join(missing_cols)}\n"
                if extra_cols:
                    msg += f"数据中有但配置中缺少的字段：{', '.join(extra_cols)}\n"
                msg += "\n是否仍要应用此配置？"

                reply = QMessageBox.question(self, "字段不匹配", msg,
                                           QMessageBox.Yes | QMessageBox.No)
                return reply == QMessageBox.Yes

            return True
        except Exception:
            return True  # 出错时默认兼容

    def _load_system_config_with_sheet_selection(self, config_name: str) -> Optional[Dict[str, Any]]:
        """加载系统配置，如果是多表配置则让用户选择工作表"""
        try:
            # 获取原始配置数据
            if config_name not in self.config_manager.configs:
                return None

            config_data = self.config_manager.configs[config_name]

            # 检查是否是多表配置
            if config_data.get("type") == "multi_sheet_user_config":
                sheets = config_data.get("sheets", {})
                if not sheets:
                    QMessageBox.warning(self, "警告", f"配置 '{config_name}' 中没有找到工作表")
                    return None

                # 如果只有一个工作表，直接使用
                if len(sheets) == 1:
                    sheet_name = next(iter(sheets.keys()))
                    return sheets[sheet_name].get("config", {})

                # 多个工作表，使用多选对话框让用户选择
                dialog = MultiSheetSelectionDialog(
                    parent=self,
                    config_name=config_name,
                    sheets_data=sheets
                )

                if dialog.exec_() == QDialog.Accepted:
                    selected_sheets = dialog.get_selected_sheets()
                    if selected_sheets:
                        # 如果选择了多个工作表，合并配置或使用第一个
                        if len(selected_sheets) == 1:
                            return sheets[selected_sheets[0]].get("config", {})
                        else:
                            # 多个工作表选择，可以合并配置或让用户进一步选择
                            # 这里先使用第一个选中的工作表配置
                            logger.info(f"用户选择了多个工作表: {selected_sheets}，使用第一个: {selected_sheets[0]}")
                            return sheets[selected_sheets[0]].get("config", {})
                    else:
                        return None
                else:
                    return None
            else:
                # 单表配置，使用原来的逻辑
                return self.config_manager.load_config(config_name)

        except Exception as e:
            logger.error(f"加载系统配置时发生错误: {e}")
            QMessageBox.critical(self, "错误", f"加载配置时发生错误：\n{str(e)}")
            return None

    def delete_configuration(self):
        """删除配置"""
        config_name = self.config_combo.currentText()
        if not config_name or config_name == "-- 选择配置 --":
            QMessageBox.warning(self, "警告", "请先选择要删除的配置")
            return
        
        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除配置 '{config_name}' 吗？",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            success = self.config_manager.delete_config(config_name)
            if success:
                QMessageBox.information(self, "成功", "配置删除成功")
                self.update_config_list()
            else:
                QMessageBox.critical(self, "错误", "配置删除失败")
    
    def update_config_list(self):
        """更新配置列表，区分系统和用户配置"""
        self.config_combo.clear()
        
        # 获取系统自动配置和用户配置
        system_configs = self.config_manager.list_configs()
        user_configs = self.config_manager.list_user_configs()
        
        if not system_configs and not user_configs:
            # 配置为空时显示引导提示
            self.config_combo.addItem("-- 暂无配置，请新建或导入 --")
            self.config_combo.setToolTip('点击"另存配置"按钮创建新配置，或使用"导入配置"加载现有配置')
            # 设置提示样式
            self.config_combo.setStyleSheet("""
                QComboBox {
                    color: #666;
                    font-style: italic;
                }
            """)
        else:
            self.config_combo.addItem("-- 选择配置 --")
            self.config_combo.setToolTip("选择已保存的配置")
            self.config_combo.setStyleSheet("")  # 恢复默认样式
            
            # 添加用户配置分组
            if user_configs:
                self.config_combo.addItem("-- 用户保存的配置 --")
                self.config_combo.model().item(self.config_combo.count() - 1).setEnabled(False)  # 禁用分组标题
                
                for config in user_configs:
                    display_text = f"📁 {config['name']}"
                    if config.get('description'):
                        display_text += f" - {config['description'][:30]}"
                    self.config_combo.addItem(display_text, f"user:{config['name']}")
            
            # 添加系统自动配置分组
            if system_configs:
                self.config_combo.addItem("-- 系统自动保存 --")
                self.config_combo.model().item(self.config_combo.count() - 1).setEnabled(False)  # 禁用分组标题
                
                for config in system_configs:
                    # 只显示 auto_save 开头的配置
                    if config["name"].startswith("auto_save_"):
                        display_text = f"🔧 {config['name']}"
                        if config.get("description"):
                            display_text += f" - {config['description'][:30]}"
                        self.config_combo.addItem(display_text, f"system:{config['name']}")
                    else:
                        # 其他系统配置（如旧的手动保存的）
                        display_text = config["name"]
                        if config.get("description"):
                            display_text += f" - {config['description'][:30]}"
                        self.config_combo.addItem(display_text, f"system:{config['name']}")
    
    def update_template_list(self):
        """更新模板列表"""
        self.template_combo.clear()
        self.template_combo.addItem("-- 选择模板（可选） --")
        
        templates = self.config_manager.list_templates()
        for template in templates:
            # 存储模板key作为userData，显示name和描述
            display_text = template["name"]
            self.template_combo.addItem(display_text, template["key"])
            # 设置每个模板项的工具提示
            if template.get("description"):
                self.template_combo.setItemData(
                    self.template_combo.count() - 1,
                    template["description"],
                    Qt.ToolTipRole
                )
    
    def load_all_sheets(self):
        """加载Excel文件的所有sheets"""
        if not self.excel_file_path:
            return
        
        try:
            # 读取所有sheets
            excel_file = pd.ExcelFile(self.excel_file_path)
            for sheet_name in excel_file.sheet_names:
                self.all_sheets_data[sheet_name] = pd.read_excel(
                    self.excel_file_path, 
                    sheet_name=sheet_name
                )
            logger.info(f"成功加载 {len(self.all_sheets_data)} 个工作表")
        except Exception as e:
            logger.error(f"加载Excel sheets失败: {e}")
    
    def on_sheet_changed(self, sheet_name: str):
        """处理sheet切换"""
        # 先保存当前工作表的配置
        if hasattr(self, 'current_sheet_name') and self.current_sheet_name != 'unknown':
            try:
                current_config = self.get_current_configuration()
                if current_config and current_config.get('field_mapping'):  # 只有有实际配置才保存
                    self.all_sheets_configs[self.current_sheet_name] = current_config
                    logger.info(f"已保存工作表 '{self.current_sheet_name}' 的配置")
            except Exception as e:
                logger.warning(f"保存当前工作表配置时出错: {e}")
        
        # 切换到新工作表
        if sheet_name in self.all_sheets_data:
            self.excel_data = self.all_sheets_data[sheet_name]
            self.current_sheet_name = sheet_name  # 保存当前工作表名称
            self.load_excel_preview()
            logger.info(f"切换到工作表: {sheet_name}")
            
            # 尝试加载该工作表的专用配置
            self._load_sheet_specific_config(sheet_name)
    
    def _save_current_sheet_config_to_cache(self):
        """保存当前工作表的配置到缓存中"""
        try:
            if hasattr(self, 'current_sheet_name') and self.current_sheet_name and self.current_sheet_name != 'unknown':
                current_config = self.get_current_configuration()
                if current_config and current_config.get('field_mapping'):
                    self.all_sheets_configs[self.current_sheet_name] = current_config
                    logger.info(f"🔧 [关键修复] 当前工作表 '{self.current_sheet_name}' 配置已保存到缓存")
                    return True
        except Exception as e:
            logger.warning(f"保存当前配置到缓存失败: {e}")
        return False

    def _load_sheet_specific_config(self, sheet_name: str):
        """加载指定工作表的专用配置"""
        try:
            # 从父窗口获取该工作表的配置
            parent = self.parent()
            if parent and hasattr(parent, 'change_data_configs') and parent.change_data_configs:
                if sheet_name in parent.change_data_configs:
                    config = parent.change_data_configs[sheet_name]
                    logger.info(f"为工作表 '{sheet_name}' 加载专用配置")
                    self.apply_saved_configuration(config)
                    return True
                else:
                    logger.info(f"工作表 '{sheet_name}' 没有专用配置")
            
            return False
            
        except Exception as e:
            logger.error(f"加载工作表专用配置失败: {e}")
            return False
    
    def create_field_type_combo(self) -> QComboBox:
        """🔧 [P0修复] 创建字段类型下拉框（包含内置和自定义类型）"""
        try:
            type_combo = QComboBox()
            type_combo.setMinimumWidth(200)  # 设置最小宽度

            # 🔧 [P0修复] 验证QComboBox创建是否成功
            if not isinstance(type_combo, QComboBox):
                logger.error(f"QComboBox创建失败，类型: {type(type_combo)}")
                raise TypeError("QComboBox创建失败")
        except Exception as e:
            logger.error(f"创建QComboBox时发生异常: {e}")
            # 创建一个最基本的QComboBox
            type_combo = QComboBox()
            type_combo.addItem("文本字符串", "text_string")
            return type_combo
        
        # 🔧 [P1修复] 设置下拉框的高度和样式，确保显示正常和垂直居中
        type_combo.setMinimumHeight(28)  # 设置最小高度
        type_combo.setMaximumHeight(32)  # 设置最大高度，留出足够空间
        type_combo.setStyleSheet("""
            QComboBox {
                padding: 1px 6px;
                border: 1px solid #ccc;
                border-radius: 3px;
                font-size: 12px;
            }
            QComboBox::drop-down {
                width: 20px;
                border-left: 1px solid #ccc;
            }
            QComboBox::down-arrow {
                width: 12px;
                height: 12px;
            }
            QComboBox QAbstractItemView {
                min-height: 25px;
                selection-background-color: #0078d4;
            }
            QComboBox QAbstractItemView::item {
                padding: 4px;
                min-height: 25px;
            }
        """)
        
        # 获取所有字段类型（内置 + 自定义）
        all_field_types = self.field_type_manager.get_all_field_types()
        
        # 分组显示
        # 1. 内置类型
        builtin_types = self.formatting_engine.get_field_types()
        for type_id, type_info in builtin_types.items():
            display_text = f"{type_info['name']} ({type_id})"
            type_combo.addItem(display_text, type_id)
            # 设置tooltip
            index = type_combo.count() - 1
            type_combo.setItemData(index, type_info.get('description', ''), Qt.ToolTipRole)
        
        # 2. 自定义类型（如果有）
        custom_types = self.field_type_manager.list_custom_field_types()
        if custom_types:
            # 添加分隔符
            type_combo.insertSeparator(type_combo.count())
            
            for type_info in custom_types:
                type_id = type_info['id']
                display_text = f"[自定义] {type_info['name']} ({type_id})"
                type_combo.addItem(display_text, type_id)
                # 设置tooltip
                index = type_combo.count() - 1
                type_combo.setItemData(index, type_info.get('description', ''), Qt.ToolTipRole)
        
        # 🔧 [P1修复] 添加字段类型变化时更新示例数据的事件连接
        type_combo.currentIndexChanged.connect(lambda: self._on_field_type_changed(type_combo))
        
        return type_combo
    
    def _on_field_type_changed(self, combo: QComboBox):
        """🔧 [P1修复] 当字段类型改变时，更新对应行的示例数据并刷新预览"""
        # 找到这个下拉框所在的行
        for row in range(self.field_table.rowCount()):
            widget = self.field_table.cellWidget(row, 1)
            if widget:
                # 检查是否是容器内的下拉框
                found = False
                if isinstance(widget, QWidget) and widget.layout():
                    for i in range(widget.layout().count()):
                        item = widget.layout().itemAt(i).widget()
                        if item == combo:
                            found = True
                            break
                elif widget == combo:
                    found = True
                
                if found:
                    # 获取选中的字段类型
                    field_type = combo.currentData()
                    if field_type:
                        # 获取对应列名
                        col_name = self.field_table.item(row, 0).text()
                        
                        # 如果有实际数据，格式化第一行数据作为示例
                        if self.excel_data is not None and col_name in self.excel_data.columns:
                            non_null_data = self.excel_data[col_name].dropna()
                            if len(non_null_data) > 0:
                                raw_value = non_null_data.iloc[0]
                                # 根据字段类型格式化示例数据
                                sample_data = self._format_sample_value(raw_value, field_type)
                            else:
                                # 如果没有实际数据，使用默认示例
                                sample_data = self._get_sample_for_field_type(field_type)
                        else:
                            # 如果没有实际数据，使用默认示例
                            sample_data = self._get_sample_for_field_type(field_type)
                        
                        # 更新示例数据列
                        self.field_table.setItem(row, 3, QTableWidgetItem(sample_data))
                        logger.info(f"行{row+1}字段类型变更为{field_type}，示例数据更新为: {sample_data}")
                        
                        # 触发预览更新
                        self.refresh_preview()
                    break
    
    def _format_sample_value(self, value, field_type: str) -> str:
        """根据字段类型格式化示例值"""
        import pandas as pd
        
        if pd.isna(value):
            return ""
        
        try:
            if field_type == "employee_id_string":
                # 工号：去除小数点，保持原始字符串
                return str(int(float(value))) if str(value).strip() else ""
            elif field_type == "salary_float":
                # 🔧 [用户需求修复] 金额：根据格式化规则决定是否使用千位分隔符
                current_config = self.get_current_configuration()
                return self._format_salary_value(value, field_type, current_config)
            elif field_type == "personnel_category_code":
                # 人员类别代码：单位数前补0
                return str(int(float(value))).zfill(2)
            elif field_type == "date_string":
                # 日期：格式化为YYYY-MM-DD
                return pd.to_datetime(value).strftime("%Y-%m-%d")
            elif field_type == "id_number_string":
                # 身份证：隐藏中间部分
                s = str(value)
                if len(s) >= 18:
                    return s[:6] + "********" + s[-4:]
                return s
            elif field_type == "name_string":
                # 姓名：去除空格
                return str(value).strip()
            else:
                # 其他：转为字符串
                return str(value)
        except Exception as e:
            logger.warning(f"格式化示例值失败: {e}")
            return str(value)
    
    def _get_sample_for_field_type(self, field_type: str) -> str:
        """🔧 [P1修复] 根据字段类型生成示例数据"""
        sample_map = {
            # 工资相关
            'salary_float': '5280.50',
            'float': '1234.56',
            'integer': '100',
            
            # 人员信息
            'employee_id_string': '19990089',  # 修复：改为纯数字格式，无小数
            'name_string': '张三',
            'id_number_string': '110101199001011234',
            'personnel_category_code': '01',  # 新增：人员类别代码
            
            # 日期相关
            'date_string': '2024-01-15',
            
            # 代码相关
            'code_string': 'CODE001',
            
            # 文本
            'text_string': '示例文本',
            'string': '文本内容'
        }
        
        # 如果有对应的示例，返回；否则返回默认文本
        return sample_map.get(field_type, '示例数据')
    
    def manage_field_types(self):
        """管理字段类型"""
        from src.gui.field_type_manager_dialog import FieldTypeManagerDialog
        
        dialog = FieldTypeManagerDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            # 刷新字段类型下拉框
            self.refresh_field_type_combos()
            QMessageBox.information(self, "提示", "字段类型已更新")
    
    def refresh_field_type_combos(self):
        """刷新所有字段类型下拉框"""
        for i in range(self.field_table.rowCount()):
            widget = self.field_table.cellWidget(i, 1)
            if widget:
                # 尝试从容器中获取下拉框
                combo = None
                if isinstance(widget, QWidget) and widget.layout():
                    # 从容器布局中获取下拉框
                    for j in range(widget.layout().count()):
                        item = widget.layout().itemAt(j).widget()
                        if isinstance(item, QComboBox):
                            combo = item
                            break
                elif isinstance(widget, QComboBox):
                    # 直接是下拉框（兼容旧代码）
                    combo = widget
                
                if combo:
                    # 保存当前选择
                    current_data = combo.currentData()
                    
                    # 重新创建下拉框
                    new_combo = self.create_field_type_combo()
                    
                    # 恢复选择
                    if current_data:
                        index = new_combo.findData(current_data)
                        if index >= 0:
                            new_combo.setCurrentIndex(index)
                    
                    # 创建新的容器实现垂直居中
                    container = QWidget()
                    layout = QVBoxLayout(container)
                    layout.setContentsMargins(2, 2, 2, 2)
                    layout.setAlignment(Qt.AlignVCenter)
                    layout.addWidget(new_combo)
                    
                    self.field_table.setCellWidget(i, 1, container)
    
    def init_default_formatting_rules(self):
        """初始化默认格式化规则"""
        default_rules = [
            ("salary_float", "小数位数", "2"),
            ("salary_float", "千位分隔符", "是"),
            ("salary_float", "负数格式", "负号"),
            ("employee_id_string", "保留前导零", "是"),
            ("employee_id_string", "最小长度", "6"),
            ("date_string", "日期格式", "%Y-%m-%d"),
        ]
        
        for field_type, rule_name, rule_value in default_rules:
            self.add_rule_to_table(field_type, rule_name, rule_value)
    
    def add_rule_to_table(self, field_type: str, rule_name: str, rule_value: str):
        """添加规则到表格"""
        row = self.rules_table.rowCount()
        self.rules_table.insertRow(row)
        
        # 字段类型
        self.rules_table.setItem(row, 0, QTableWidgetItem(field_type))
        
        # 规则名称
        self.rules_table.setItem(row, 1, QTableWidgetItem(rule_name))
        
        # 规则值（可编辑）
        value_item = QTableWidgetItem(rule_value)
        self.rules_table.setItem(row, 2, value_item)
        
        # 操作按钮
        delete_btn = QPushButton("删除")
        delete_btn.clicked.connect(lambda: self.delete_rule(row))
        self.rules_table.setCellWidget(row, 3, delete_btn)
    
    def add_formatting_rule(self):
        """添加新的格式化规则"""
        dialog = AddRuleDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            field_type, rule_name, rule_value = dialog.get_rule_data()
            self.add_rule_to_table(field_type, rule_name, rule_value)
            # 🔧 [用户需求修复] 添加规则后刷新预览
            self.refresh_preview()
    
    def delete_rule(self, row: int):
        """删除指定行的规则"""
        # 由于删除行后索引会变化，需要重新获取当前行
        button = self.sender()
        if button:
            index = self.rules_table.indexAt(button.pos())
            if index.isValid():
                self.rules_table.removeRow(index.row())
                # 🔧 [用户需求修复] 删除规则后刷新预览
                self.refresh_preview()
    
    def clear_formatting_rules(self):
        """清空所有格式化规则"""
        reply = QMessageBox.question(
            self, "确认", "确定要清空所有格式化规则吗？",
            QMessageBox.Yes | QMessageBox.No
        )
        if reply == QMessageBox.Yes:
            self.rules_table.setRowCount(0)
            # 🔧 [用户需求修复] 清空规则后刷新预览
            self.refresh_preview()
    
    def apply_rule_template(self):
        """应用规则模板"""
        templates = {
            "标准工资表": [
                ("salary_float", "小数位数", "2"),
                ("salary_float", "千位分隔符", "是"),
                ("employee_id_string", "保留前导零", "是"),
                ("date_string", "日期格式", "%Y-%m-%d"),
            ],
            "精确计算": [
                ("salary_float", "小数位数", "4"),
                ("salary_float", "千位分隔符", "否"),
                ("float", "科学计数法", "否"),
            ],
            "国际格式": [
                ("salary_float", "小数位数", "2"),
                ("salary_float", "千位分隔符", "是"),
                ("date_string", "日期格式", "%d/%m/%Y"),
                ("float", "小数点符号", "."),
            ]
        }
        
        template_name, ok = QInputDialog.getItem(
            self, "选择模板", "请选择格式化规则模板:",
            list(templates.keys()), 0, False
        )
        
        if ok and template_name:
            # 清空现有规则
            self.rules_table.setRowCount(0)
            # 应用模板规则
            for rule in templates[template_name]:
                self.add_rule_to_table(*rule)

            # 🔧 [用户需求修复] 应用模板后刷新预览
            self.refresh_preview()
            QMessageBox.information(self, "成功", f"已应用模板: {template_name}")
    
    def get_formatting_rules(self) -> Dict[str, Any]:
        """获取当前的格式化规则"""
        rules = {}
        for row in range(self.rules_table.rowCount()):
            field_type = self.rules_table.item(row, 0).text()
            rule_name = self.rules_table.item(row, 1).text()
            rule_value = self.rules_table.item(row, 2).text()
            
            if field_type not in rules:
                rules[field_type] = {}
            rules[field_type][rule_name] = rule_value
        
        return rules
    
    def apply_formatting_rules_to_table(self, rules: Dict[str, Any]):
        """将格式化规则应用到动态规则表"""
        # 清空现有规则
        self.rules_table.setRowCount(0)
        
        # 将规则添加到表格
        for field_type, field_rules in rules.items():
            if isinstance(field_rules, dict):
                for rule_name, rule_value in field_rules.items():
                    self.add_rule_to_table(field_type, rule_name, str(rule_value))
            else:
                # 处理简单值
                self.add_rule_to_table(field_type, "value", str(field_rules))
    
    def open_config_wizard(self):
        """打开配置向导"""
        try:
            from src.gui.config_wizard_dialog import ConfigWizardDialog
            
            wizard = ConfigWizardDialog(self)
            wizard.config_completed.connect(self.apply_wizard_config)
            
            if wizard.exec_() == QDialog.Accepted:
                QMessageBox.information(self, "成功", "配置向导已完成！")
        except Exception as e:
            logger.error(f"打开配置向导失败: {e}")
            QMessageBox.critical(self, "错误", f"无法打开配置向导: {e}")
    
    def _apply_config_to_table(self, config: Dict[str, Any]):
        """实际应用配置到表格"""
        try:
            # 应用字段类型配置
            if 'field_types' in config and self.field_table.rowCount() > 0:
                logger.info(f"应用字段类型配置，共{len(config['field_types'])}个字段")
                logger.debug(f"配置内容: {config['field_types']}")
                
                for i in range(self.field_table.rowCount()):
                    excel_field = self.field_table.item(i, 0).text()
                    logger.debug(f"处理第{i}行，字段名: {excel_field}")
                    
                    if excel_field in config['field_types']:
                        field_type = config['field_types'][excel_field]
                        logger.debug(f"找到字段配置: {excel_field} -> {field_type}")
                        widget = self.field_table.cellWidget(i, 1)
                        
                        # 从容器获取下拉框
                        type_combo = None
                        if widget:
                            if isinstance(widget, QWidget) and widget.layout():
                                for j in range(widget.layout().count()):
                                    item = widget.layout().itemAt(j).widget()
                                    if isinstance(item, QComboBox):
                                        type_combo = item
                                        break
                            elif isinstance(widget, QComboBox):
                                type_combo = widget
                        
                        if type_combo:
                            # 设置字段类型
                            success = self._set_combo_by_data(type_combo, field_type)
                            logger.info(f"设置字段 {excel_field} 类型为 {field_type}: {'成功' if success else '失败'}")
            
            # 应用字段映射
            if 'field_mapping' in config and self.field_table.rowCount() > 0:
                for i in range(self.field_table.rowCount()):
                    excel_field = self.field_table.item(i, 0).text()
                    if excel_field in config['field_mapping']:
                        db_field = config['field_mapping'][excel_field]
                        self.field_table.setItem(i, 2, QTableWidgetItem(db_field))
            
            # 应用格式化规则
            if 'formatting_rules' in config:
                self.apply_formatting_rules_to_table(config['formatting_rules'])
            
            # 刷新预览
            self.refresh_preview()
            
            logger.info("配置应用到表格成功")
            
        except Exception as e:
            logger.error(f"应用配置到表格失败: {e}")
    
    def apply_saved_configuration(self, config: Dict[str, Any]):
        """应用之前保存的配置（从主对话框传入）"""
        try:
            if not config:
                return
            
            logger.info(f"应用之前保存的配置，配置包含: {config.keys()}")
            
            # 直接应用配置到现有的表格控件
            self._apply_config_to_table(config)
            
        except Exception as e:
            logger.error(f"应用保存的配置失败: {e}")
    
    def apply_wizard_config(self, config: Dict[str, Any]):
        """应用向导生成的配置"""
        try:
            # 应用数据类型
            if 'data_type' in config:
                logger.info(f"应用数据类型: {config['data_type']}")
            
            # 应用选中的字段
            if 'selected_fields' in config:
                # 清空现有字段配置
                self.field_table.setRowCount(0)
                # 添加选中的字段
                for field in config['selected_fields']:
                    self.field_table.insertRow(self.field_table.rowCount())
                    row = self.field_table.rowCount() - 1
                    self.field_table.setRowHeight(row, 50)  # 设置新行的高度
                    self.field_table.setItem(row, 0, QTableWidgetItem(field))
                    
                    # 创建字段类型下拉框
                    type_combo = self.create_field_type_combo()
                    self.field_table.setCellWidget(row, 1, type_combo)
                    
                    # 数据库字段名
                    self.field_table.setItem(row, 2, QTableWidgetItem(field))
                    
                    # 示例数据（空）
                    self.field_table.setItem(row, 3, QTableWidgetItem(""))
            
            # 应用格式化规则
            if 'formatting_rules' in config:
                template = config['formatting_rules'].get('template', 'standard')
                # 根据模板应用相应的规则
                self.apply_rule_template_by_name(template)
            
            logger.info("向导配置已成功应用")
            
        except Exception as e:
            logger.error(f"应用向导配置失败: {e}")
            QMessageBox.warning(self, "警告", f"部分配置应用失败: {e}")
    
    def apply_rule_template_by_name(self, template_name: str):
        """根据模板名称应用规则"""
        template_map = {
            "standard": "标准工资表",
            "precise": "精确计算",
            "international": "国际格式"
        }
        
        if template_name in template_map:
            # 清空现有规则
            self.rules_table.setRowCount(0)
            
            # 应用对应的模板
            templates = {
                "standard": [
                    ("salary_float", "小数位数", "2"),
                    ("salary_float", "千位分隔符", "是"),
                    ("employee_id_string", "保留前导零", "是"),
                    ("date_string", "日期格式", "%Y-%m-%d"),
                ],
                "precise": [
                    ("salary_float", "小数位数", "4"),
                    ("salary_float", "千位分隔符", "否"),
                    ("float", "科学计数法", "否"),
                ],
                "international": [
                    ("salary_float", "小数位数", "2"),
                    ("salary_float", "千位分隔符", "是"),
                    ("date_string", "日期格式", "%d/%m/%Y"),
                    ("float", "小数点符号", "."),
                ]
            }
            
            if template_name in templates:
                for rule in templates[template_name]:
                    self.add_rule_to_table(*rule)


class AddRuleDialog(QDialog):
    """添加规则对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("添加格式化规则")
        self.setModal(True)
        self.init_ui()
    
    def init_ui(self):
        layout = QFormLayout()
        
        # 字段类型选择
        self.field_type_combo = QComboBox()
        self.field_type_combo.addItems([
            "text_string",
            "employee_id_string",
            "name_string",
            "salary_float",
            "date_string",
            "code_string",
            "id_number_string",
            "float",
            "integer"
        ])
        layout.addRow("字段类型:", self.field_type_combo)
        
        # 规则名称
        self.rule_name_edit = QLineEdit()
        self.rule_name_edit.setPlaceholderText("例如: 小数位数")
        layout.addRow("规则名称:", self.rule_name_edit)
        
        # 规则值
        self.rule_value_edit = QLineEdit()
        self.rule_value_edit.setPlaceholderText("例如: 2")
        layout.addRow("规则值:", self.rule_value_edit)
        
        # 按钮
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel,
            Qt.Horizontal
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
        self.setLayout(layout)
    
    def get_rule_data(self) -> Tuple[str, str, str]:
        """获取规则数据"""
        return (
            self.field_type_combo.currentText(),
            self.rule_name_edit.text(),
            self.rule_value_edit.text()
        )


# 测试代码
if __name__ == "__main__":
    from PyQt5.QtWidgets import QApplication
    import sys
    
    app = QApplication(sys.argv)
    
    # 创建测试数据
    test_data = pd.DataFrame({
        "工号": ["001", "002", "003"],
        "姓名": ["张三", "李四", "王五"],
        "部门": ["财务部", "人事部", "技术部"],
        "岗位工资": [5000, 6000, 7000],
        "薪级工资": [2000, 2500, 3000],
        "津贴": [1000, 1200, 1500]
    })
    
    dialog = ChangeDataConfigDialog(test_data)
    dialog.show()
    
    sys.exit(app.exec_())