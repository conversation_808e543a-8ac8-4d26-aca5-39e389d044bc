{"tt1": {"name": "tt1", "description": "多工作表配置 - 4 个工作表，共 94 个字段", "created_at": "2025-08-27T18:51:46.939639", "updated_at": "2025-08-27T19:43:51.566340", "version": "2.0", "type": "multi_sheet_user_config", "sheet_count": 4, "sheets": {"A岗职工": {"sheet_name": "A岗职工", "field_count": 21, "config": {"field_mapping": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "field_types": {"序号": "integer", "工号": "employee_id_string", "姓名": "name_string", "部门名称": "text_string", "人员类别": "text_string", "人员类别代码": "personnel_category_code", "2025年岗位工资": "salary_float", "2025年校龄工资": "salary_float", "津贴": "salary_float", "结余津贴": "salary_float", "2025年基础性绩效": "salary_float", "卫生费": "salary_float", "2025年生活补贴": "salary_float", "车补": "salary_float", "2025年奖励性绩效预发": "salary_float", "补发": "salary_float", "借支": "salary_float", "应发工资": "salary_float", "2025公积金": "salary_float", "保险扣款": "salary_float", "代扣代存养老保险": "salary_float"}, "formatting_rules": {"salary_float": {"小数位数": "2", "负数格式": "负号"}, "employee_id_string": {"保留前导零": "是", "最小长度": "6"}, "date_string": {"日期格式": "%Y-%m-%d"}}}}, "离休人员工资表": {"sheet_name": "离休人员工资表", "field_count": 23, "config": {"field_mapping": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "人员类别": "人员类别", "2025年岗位工资": "2025年岗位工资", "2025年薪级工资": "2025年薪级工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "交通补贴": "交通补贴", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "车补": "车补", "通讯补贴": "通讯补贴", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "代扣代存养老保险": "代扣代存养老保险"}, "field_types": {"序号": "integer", "工号": "employee_id_string", "姓名": "name_string", "部门名称": "text_string", "人员类别代码": "personnel_category_code", "人员类别": "text_string", "2025年岗位工资": "salary_float", "2025年薪级工资": "salary_float", "津贴": "salary_float", "结余津贴": "salary_float", "2025年基础性绩效": "salary_float", "卫生费": "salary_float", "交通补贴": "salary_float", "物业补贴": "salary_float", "住房补贴": "salary_float", "车补": "salary_float", "通讯补贴": "salary_float", "2025年奖励性绩效预发": "salary_float", "补发": "salary_float", "借支": "salary_float", "应发工资": "salary_float", "2025公积金": "salary_float", "代扣代存养老保险": "salary_float"}, "formatting_rules": {"salary_float": {"小数位数": "2", "千位分隔符": "是", "负数格式": "负号"}, "employee_id_string": {"保留前导零": "是", "最小长度": "6"}, "date_string": {"日期格式": "%Y-%m-%d"}}}}, "退休人员工资表": {"sheet_name": "退休人员工资表", "field_count": 27, "config": {"field_mapping": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "基本退休费": "基本退休费", "津贴": "津贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "护理费": "护理费", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "公积": "公积", "保险扣款": "保险扣款", "备注": "备注"}, "field_types": {"序号": "integer", "人员代码": "employee_id_string", "姓名": "name_string", "部门名称": "text_string", "人员类别代码": "personnel_category_code", "基本退休费": "salary_float", "津贴": "salary_float", "结余津贴": "salary_float", "离退休生活补贴": "salary_float", "护理费": "salary_float", "物业补贴": "salary_float", "住房补贴": "salary_float", "增资预付": "salary_float", "2016待遇调整": "salary_float", "2017待遇调整": "salary_float", "2018待遇调整": "salary_float", "2019待遇调整": "salary_float", "2020待遇调整": "salary_float", "2021待遇调整": "salary_float", "2022待遇调整": "salary_float", "2023待遇调整": "salary_float", "补发": "salary_float", "借支": "salary_float", "应发工资": "salary_float", "公积": "salary_float", "保险扣款": "salary_float", "备注": "text_string"}, "formatting_rules": {"salary_float": {"小数位数": "2", "千位分隔符": "是", "负数格式": "负号"}, "employee_id_string": {"保留前导零": "是", "最小长度": "6"}, "date_string": {"日期格式": "%Y-%m-%d"}}}}, "全部在职人员工资表": {"sheet_name": "全部在职人员工资表", "field_count": 23, "config": {"field_mapping": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "人员类别": "人员类别", "2025年岗位工资": "2025年岗位工资", "2025年薪级工资": "2025年薪级工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "交通补贴": "交通补贴", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "车补": "车补", "通讯补贴": "通讯补贴", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "代扣代存养老保险": "代扣代存养老保险"}, "field_types": {"序号": "integer", "工号": "employee_id_string", "姓名": "name_string", "部门名称": "text_string", "人员类别代码": "personnel_category_code", "人员类别": "text_string", "2025年岗位工资": "salary_float", "2025年薪级工资": "salary_float", "津贴": "salary_float", "结余津贴": "salary_float", "2025年基础性绩效": "salary_float", "卫生费": "salary_float", "交通补贴": "salary_float", "物业补贴": "salary_float", "住房补贴": "salary_float", "车补": "salary_float", "通讯补贴": "salary_float", "2025年奖励性绩效预发": "salary_float", "补发": "salary_float", "借支": "salary_float", "应发工资": "salary_float", "2025公积金": "salary_float", "代扣代存养老保险": "salary_float"}, "formatting_rules": {"salary_float": {"小数位数": "2", "千位分隔符": "是", "负数格式": "负号"}, "employee_id_string": {"保留前导零": "是", "最小长度": "6"}, "date_string": {"日期格式": "%Y-%m-%d"}}}}}, "imported_at": "2025-08-27T19:43:51.566340"}}