"""
应用状态服务 (Application State Service)

作为应用状态的唯一"真相来源"（Single Source of Truth），负责管理和广播应用级别的状态变化。
所有需要响应状态变化的组件都应连接到此服务的信号。
"""

from PyQt5.QtCore import QObject, pyqtSignal
import pandas as pd
from typing import Optional, Any

class ApplicationStateService(QObject):
    """
    管理全局应用状态，并在状态变化时发出信号。
    这是一个非GUI类，可以在应用的任何地方安全地使用。
    """

    # --- Signals ---
    # 导航路径变化信号，参数为新的路径字符串 (e.g., "工资表 > 2025年 > 05月 > 全部在职人员")
    navigation_path_changed = pyqtSignal(str)

    # 数据加载状态变化信号，参数为布尔值 (True: 正在加载, False: 加载完成)
    data_loading_changed = pyqtSignal(bool)

    # 数据更新信号，参数为新的数据 (pandas.DataFrame)
    data_updated = pyqtSignal(object)

    # 状态栏消息更新信号，参数为 (消息文本, 消息类型) e.g., ("加载成功", "success")
    status_message_changed = pyqtSignal(str, str)
    
    # 当前表名变化信号
    table_name_changed = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self._current_navigation_path: Optional[str] = None
        self._current_table_name: Optional[str] = None
        self._is_loading: bool = False
        self._current_data: Optional[pd.DataFrame] = None
        self._status_message: tuple[str, str] = ("", "info")

    # --- State Mutators ---
    def set_navigation_path(self, path: Optional[str]):
        """设置当前导航路径并发出信号。"""
        if self._current_navigation_path != path:
            self._current_navigation_path = path
            if path:
                self.navigation_path_changed.emit(path)

    def set_table_name(self, table_name: Optional[str]):
        """设置当前操作的表名并发出信号。"""
        if self._current_table_name != table_name:
            self._current_table_name = table_name
            if table_name:
                self.table_name_changed.emit(table_name)

    def set_loading(self, is_loading: bool):
        """设置数据加载状态并发出信号。"""
        if self._is_loading != is_loading:
            self._is_loading = is_loading
            self.data_loading_changed.emit(is_loading)

    def set_data(self, data: Optional[pd.DataFrame]):
        """设置当前数据并发出信号。"""
        self._current_data = data
        self.data_updated.emit(data)

    def set_status_message(self, message: str, msg_type: str = "info"):
        """设置状态栏消息并发出信号。"""
        self._status_message = (message, msg_type)
        self.status_message_changed.emit(message, msg_type)

    # --- State Accessors (Getters) ---
    def get_current_navigation_path(self) -> Optional[str]:
        """获取当前导航路径。"""
        return self._current_navigation_path

    def get_current_table_name(self) -> Optional[str]:
        """获取当前操作的表名。"""
        return self._current_table_name

    def is_loading(self) -> bool:
        """获取当前是否正在加载数据。"""
        return self._is_loading

    def get_current_data(self) -> Optional[pd.DataFrame]:
        """获取当前数据。"""
        return self._current_data

# --- Singleton Instance ---
# 提供一个全局单例，方便在应用各处访问
_state_service_instance = None

def get_application_state_service() -> ApplicationStateService:
    """获取应用状态服务的全局单例。"""
    global _state_service_instance
    if _state_service_instance is None:
        _state_service_instance = ApplicationStateService()
    return _state_service_instance 