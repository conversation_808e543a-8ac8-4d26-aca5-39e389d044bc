2025-08-27 16:55:31.528 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-08-27 16:55:31.528 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-08-27 16:55:31.528 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-08-27 16:55:31.528 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-08-27 16:55:31.528 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-08-27 16:55:31.528 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-27 16:55:34.068 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-08-27 16:55:34.068 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-08-27 16:55:34.068 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-08-27 16:55:34.068 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-08-27 16:55:34.068 | INFO     | __main__:setup_app_logging:423 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-08-27 16:55:34.068 | INFO     | __main__:main:487 | 初始化核心管理器...
2025-08-27 16:55:34.078 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-27 16:55:34.078 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-27 16:55:34.078 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-27 16:55:34.079 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-27 16:55:34.079 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-27 16:55:34.087 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-27 16:55:34.087 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: E:\project\case\salary_changes\salary_changes\data\db\salary_system.db
2025-08-27 16:55:34.087 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-27 16:55:34.087 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-27 16:55:34.087 | INFO     | __main__:main:492 | 核心管理器初始化完成。
2025-08-27 16:55:34.087 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-08-27 16:55:34.087 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-08-27 16:55:34.087 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-27 16:55:34.087 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-08-27 16:55:34.087 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-08-27 16:55:34.087 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-08-27 16:55:34.087 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-08-27 16:55:34.087 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:11817 | 🔧 [P2-3] 错误恢复策略注册完成
2025-08-27 16:55:34.087 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-27 16:55:34.087 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:11672 | 🔧 [P2-3] 错误处理机制设置完成
2025-08-27 16:55:34.087 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:11710 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-08-27 16:55:34.148 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-08-27 16:55:34.148 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-08-27 16:55:34.148 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-27 16:55:34.148 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:149 | 🔧 [配置修复] 创建了完整的默认配置，包含基本字段映射和模板
2025-08-27 16:55:34.148 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-27 16:55:34.148 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-27 16:55:34.148 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-27 16:55:34.162 | INFO     | src.core.field_mapping_manager:_load_config:89 | 🔧 [P3优化] 字段映射配置加载成功
2025-08-27 16:55:34.162 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-08-27 16:55:34.162 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-27 16:55:34.162 | INFO     | src.modules.format_management.field_registry:__init__:95 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-08-27 16:55:34.162 | INFO     | src.core.unified_mapping_service:__init__:45 | UnifiedMappingService 初始化完成
2025-08-27 16:55:34.162 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-08-27 16:55:34.167 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-08-27 16:55:34.170 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-27 16:55:34.171 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-27 16:55:34.171 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-08-27 16:55:34.171 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 23.2ms
2025-08-27 16:55:34.180 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-08-27 16:55:34.180 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-08-27 16:55:34.180 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-08-27 16:55:34.181 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-08-27 16:55:34.181 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-08-27 16:55:34.181 | INFO     | src.gui.prototype.prototype_main_window:__init__:3650 | 🚀 性能管理器已集成
2025-08-27 16:55:34.182 | INFO     | src.gui.prototype.prototype_main_window:__init__:3652 | ✅ 新架构集成成功！
2025-08-27 16:55:34.182 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3765 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-08-27 16:55:34.183 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3730 | ✅ 新架构事件监听器设置完成
2025-08-27 16:55:34.183 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-08-27 16:55:34.184 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-08-27 16:55:34.184 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-08-27 16:55:34.569 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2726 | 菜单栏创建完成
2025-08-27 16:55:34.569 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: E:\project\case\salary_changes\salary_changes\user_preferences.json
2025-08-27 16:55:34.569 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: user_preferences.json
2025-08-27 16:55:34.569 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-08-27 16:55:34.577 | INFO     | src.gui.prototype.prototype_main_window:__init__:2701 | 菜单栏管理器初始化完成
2025-08-27 16:55:34.577 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-08-27 16:55:34.577 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5458 | 管理器设置完成，包含增强版表头管理器
2025-08-27 16:55:34.577 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5463 | 🔧 开始应用窗口级Material Design样式...
2025-08-27 16:55:34.577 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-08-27 16:55:34.580 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-08-27 16:55:34.580 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5470 | ✅ 窗口级样式应用成功
2025-08-27 16:55:34.580 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5511 | ✅ 响应式样式监听设置完成
2025-08-27 16:55:34.583 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-27 16:55:34.584 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-27 16:55:34.585 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-27 16:55:34.586 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-27 16:55:34.588 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-27 16:55:34.605 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-27 16:55:34.619 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1835 | 🏠 使用示例数据结构加载导航（首次启动或暂无数据时显示）
2025-08-27 16:55:34.619 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:2037 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-27 16:55:34.620 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-27 16:55:34.620 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:924 | 恢复导航状态: 0个展开项
2025-08-27 16:55:34.621 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月', '工资表 > 2025年 > 05月 > 全部在职人员']
2025-08-27 16:55:34.621 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-27 16:55:34.622 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-27 16:55:34.623 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-27 16:55:34.625 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-27 16:55:34.627 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:2037 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-27 16:55:34.628 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-27 16:55:34.628 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-27 16:55:34.629 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-27 16:55:34.629 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 16:55:34.629 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1601 | 🔧 [P1-2修复] 检查表状态失败，1.0s后重试...
2025-08-27 16:55:34.630 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1405 | 🔧 [P1-2修复] 未找到最新工资数据路径（可能是首次启动）
2025-08-27 16:55:34.751 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-08-27 16:55:34.751 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2142 | 🚨 [架构修复] 启用模型数据同步机制
2025-08-27 16:55:34.753 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1366 | 快捷键注册完成: 18/18 个
2025-08-27 16:55:34.753 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1809 | 拖拽排序管理器初始化完成
2025-08-27 16:55:34.763 | INFO     | src.modules.data_management.data_flow_validator:__init__:85 | 🔧 [数据验证器] 初始化完成，验证级别: lenient
2025-08-27 16:55:34.764 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-08-27 16:55:34.764 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-08-27 16:55:34.765 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2195 | 🔧 [P0-紧急修复] 数据流验证器初始化成功（宽松模式）
2025-08-27 16:55:34.765 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-08-27 16:55:34.765 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-27 16:55:34.766 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-27 16:55:34.766 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2247 | 🎯 [统一格式管理] 使用统一格式管理器
2025-08-27 16:55:34.770 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:370 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-27 16:55:34.771 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-08-27 16:55:34.773 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2294 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-08-27 16:55:34.774 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1553 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-08-27 16:55:34.775 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1554 | 🔧 [列宽保存修复] 配置文件绝对路径: E:\project\case\salary_changes\salary_changes\state\column_widths.json
2025-08-27 16:55:34.775 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1555 | 🔧 [列宽保存修复] 配置文件存在: False
2025-08-27 16:55:34.775 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1556 | 🔧 [列宽保存修复] 父目录存在: True
2025-08-27 16:55:34.776 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1557 | 🔧 [列宽保存修复] 当前工作目录: E:\project\case\salary_changes\salary_changes
2025-08-27 16:55:34.776 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2301 | 列宽管理器初始化完成
2025-08-27 16:55:34.777 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2428 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-08-27 16:55:34.778 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2315 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-08-27 16:55:34.778 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-27 16:55:34.779 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-27 16:55:34.779 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-27 16:55:34.782 | INFO     | src.gui.prototype.widgets.pagination_state_manager:__init__:78 | 🔧 [P1优化] 分页状态管理器初始化完成
2025-08-27 16:55:34.785 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 0
2025-08-27 16:55:34.785 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2838 | 🔧 [P1优化] 表切换操作: None -> default_table
2025-08-27 16:55:34.786 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-27 16:55:34.789 | INFO     | src.gui.prototype.widgets.table_header_cache:__init__:67 | 🔧 [P1优化] 表头缓存管理器初始化完成，最大缓存: 100
2025-08-27 16:55:34.789 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2890 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-27 16:55:34.790 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-27 16:55:34.795 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-27 16:55:34.796 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-27 16:55:34.796 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-27 16:55:34.796 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-27 16:55:34.797 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-27 16:55:34.798 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-08-27 16:55:34.799 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 19.0ms
2025-08-27 16:55:34.799 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-27 16:55:34.800 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-27 16:55:34.800 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1710 | 🔧 [列宽保存修复] 配置文件不存在: E:\project\case\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-08-27 16:55:34.801 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-27 16:55:34.801 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-27 16:55:34.806 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-08-27 16:55:34.810 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-08-27 16:55:34.811 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-08-27 16:55:34.838 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:594 | 控制面板按钮信号连接完成
2025-08-27 16:55:34.863 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-27 16:55:34.863 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: E:\project\case\salary_changes\salary_changes\user_preferences.json
2025-08-27 16:55:34.864 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5420 | 快捷键设置完成
2025-08-27 16:55:34.865 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5377 | 主窗口UI设置完成。
2025-08-27 16:55:34.865 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5614 | 🔧 [全局排序] 全局排序开关连接成功
2025-08-27 16:55:34.866 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5646 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-08-27 16:55:34.866 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5658 | ✅ 已连接分页刷新信号到主窗口
2025-08-27 16:55:34.866 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5659 | ✅ 已连接分页组件事件到新架构
2025-08-27 16:55:34.867 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5670 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-08-27 16:55:34.868 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5673 | 信号连接设置完成
2025-08-27 16:55:34.868 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6849 | 🔧 [P1-2修复] 发现 2 个表的配置
2025-08-27 16:55:34.869 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: salary_data_2025_07_active_employees, 10个字段
2025-08-27 16:55:34.869 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: active_employees, 5个字段
2025-08-27 16:55:34.870 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6859 | ✅ [P1-2修复] 已加载字段映射信息，共2个表的映射
2025-08-27 16:55:34.874 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-27 16:55:34.875 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-27 16:55:34.876 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-27 16:55:34.876 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-27 16:55:34.877 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-27 16:55:34.877 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-27 16:55:34.878 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-27 16:55:34.878 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-27 16:55:34.878 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-27 16:55:34.879 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 3.0ms
2025-08-27 16:55:34.879 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-27 16:55:34.880 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-27 16:55:34.880 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1710 | 🔧 [列宽保存修复] 配置文件不存在: E:\project\case\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-08-27 16:55:34.881 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-27 16:55:34.881 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-27 16:55:34.882 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-27 16:55:34.883 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8551 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-08-27 16:55:34.883 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-27 16:55:34.884 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-27 16:55:34.884 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-27 16:55:34.887 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-27 16:55:34.889 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-27 16:55:34.890 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 22
2025-08-27 16:55:34.891 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-27 16:55:34.891 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-27 16:55:34.891 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-27 16:55:34.892 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-27 16:55:34.892 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 5.0ms
2025-08-27 16:55:34.893 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-27 16:55:34.893 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-27 16:55:34.894 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1710 | 🔧 [列宽保存修复] 配置文件不存在: E:\project\case\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-08-27 16:55:34.894 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-27 16:55:34.894 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-27 16:55:34.895 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8569 | 已显示标准空表格，表头数量: 22
2025-08-27 16:55:34.895 | INFO     | src.gui.prototype.prototype_main_window:__init__:3704 | 原型主窗口初始化完成
2025-08-27 16:55:34.920 | INFO     | __main__:main:514 | 应用程序启动成功
2025-08-27 16:55:34.925 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-27 16:55:35.041 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-27 16:55:35.042 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-27 16:55:35.104 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-08-27 16:55:35.104 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2079 | MainWorkspaceArea 响应式适配: sm
2025-08-27 16:55:35.421 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1474 | 执行延迟的自动选择最新数据...
2025-08-27 16:55:35.428 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 16:55:35.429 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1497 | 延迟自动选择最新数据失败，可能没有可用数据
2025-08-27 16:55:35.608 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9453 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet']
2025-08-27 16:55:35.609 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9363 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-27 16:55:35.610 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9377 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet']
2025-08-27 16:55:35.610 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:9911 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet']
2025-08-27 16:55:35.618 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9383 | 🔧 [P0-1] 智能显示亮度修复完成
2025-08-27 16:55:35.630 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 16:55:36.429 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 16:55:36.430 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1612 | 🔧 [P1-2修复] 3次重试已用尽，可能数据导入尚未完成
2025-08-27 16:55:38.247 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:654 | 数据导入功能被触发，发出 import_requested 信号。
2025-08-27 16:55:38.247 | INFO     | src.gui.prototype.prototype_main_window:_get_suggested_target_path:8374 | 检测到当前在工资表TAB，生成工资表默认路径
2025-08-27 16:55:38.247 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5906 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 08月 > 全部在职人员。打开导入对话框。
2025-08-27 16:55:38.247 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-08-27 16:55:38.257 | INFO     | src.modules.data_import.change_data_config_manager:_save_templates:94 | 模板已保存到 E:\project\case\salary_changes\salary_changes\state\change_data_configs\templates.json
2025-08-27 16:55:38.257 | INFO     | src.modules.data_import.change_data_config_manager:__init__:43 | 配置管理器初始化完成，配置目录: E:\project\case\salary_changes\salary_changes\state\change_data_configs
2025-08-27 16:55:38.257 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:78 | 多Sheet导入器初始化完成
2025-08-27 16:55:38.257 | INFO     | src.gui.widgets.target_selection_widget:_save_config:285 | 配置保存成功
2025-08-27 16:55:38.268 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-27 16:55:38.297 | INFO     | src.gui.main_dialogs:_get_template_fields:2010 | 🔧 [P3-修复] ConfigSyncManager未初始化，使用默认字段模板
2025-08-27 16:55:38.297 | INFO     | src.gui.main_dialogs:_init_field_mapping:1997 | 🔧 [P3-修复] 未找到字段模板，使用内置默认字段列表
2025-08-27 16:55:38.333 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-08-27 16:55:38.333 | INFO     | src.modules.data_import.import_defaults_manager:get_smart_defaults_for_category:244 | 检测到工资表导入，设置table_template为salary_data
2025-08-27 16:55:38.334 | INFO     | src.gui.main_dialogs:_apply_default_settings:2616 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'separate_tables', 'table_template': 'salary_data'}
2025-08-27 16:55:38.335 | INFO     | src.gui.main_dialogs:_setup_tooltips:2989 | 工具提示设置完成
2025-08-27 16:55:38.335 | INFO     | src.gui.main_dialogs:_setup_shortcuts:3028 | 快捷键设置完成
2025-08-27 16:55:38.336 | INFO     | src.gui.main_dialogs:__init__:82 | 数据导入对话框初始化完成。
2025-08-27 16:55:38.336 | INFO     | src.modules.data_import.multi_sheet_importer:set_config_sync_manager:87 | 🔧 [修复] ConfigSyncManager已设置到MultiSheetImporter
2025-08-27 16:55:38.336 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5917 | 🔧 [P0-修复] ConfigSyncManager已设置到数据导入对话框
2025-08-27 16:55:40.167 | INFO     | src.gui.main_dialogs:_on_target_changed:2522 | 目标位置已更新: 异动人员表 > 2025年 > 08月 > 全部在职人员
2025-08-27 16:55:40.168 | INFO     | src.gui.main_dialogs:_on_change_mode_changed:2284 | 🔧 [P2-1修复] 异动表处理模式已切换为: 动态处理（保持原始）
2025-08-27 16:55:40.169 | INFO     | src.gui.main_dialogs:_on_target_changed:2541 | 数据期间自动同步为: 2025-08
2025-08-27 16:55:40.169 | INFO     | src.gui.main_dialogs:_on_target_changed:2561 | 数据描述自动生成: 2025年08月全部在职人员工资
2025-08-27 16:55:42.102 | INFO     | src.gui.main_dialogs:_on_target_changed:2522 | 目标位置已更新: 异动人员表 > 2025年 > 12月 > 全部在职人员
2025-08-27 16:55:42.103 | INFO     | src.gui.main_dialogs:_on_target_changed:2541 | 数据期间自动同步为: 2025-12
2025-08-27 16:55:42.104 | INFO     | src.gui.main_dialogs:_on_target_changed:2561 | 数据描述自动生成: 2025年12月全部在职人员工资
2025-08-27 16:55:47.472 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-27 16:55:48.487 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-08-27 16:55:48.487 | INFO     | src.gui.main_dialogs:_auto_select_sheet_by_category:2651 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-08-27 16:55:53.838 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-27 16:55:53.838 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-27 16:55:53.838 | INFO     | src.utils.log_config:log_file_operation:336 | 文件E:\project\case\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-27 16:55:53.895 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 23列 (列过滤: 否)
2025-08-27 16:55:53.897 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-27 16:55:53.899 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-08-27 16:55:53.899 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1397行 x 23列
2025-08-27 16:55:53.901 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-27 16:55:53.903 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-08-27 16:55:53.903 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1396行 × 23列
2025-08-27 16:55:53.918 | INFO     | src.modules.data_import.change_data_config_manager:__init__:43 | 配置管理器初始化完成，配置目录: E:\project\case\salary_changes\salary_changes\state\change_data_configs
2025-08-27 16:55:53.918 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: number
2025-08-27 16:55:53.918 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: string
2025-08-27 16:55:53.918 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: date
2025-08-27 16:55:53.918 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: code
2025-08-27 16:55:53.918 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: custom
2025-08-27 16:55:53.918 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: salary_float - 工资金额
2025-08-27 16:55:53.918 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: employee_id_string - 工号
2025-08-27 16:55:53.918 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: name_string - 姓名
2025-08-27 16:55:53.918 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: date_string - 日期
2025-08-27 16:55:53.918 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: id_number_string - 身份证号
2025-08-27 16:55:53.918 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: code_string - 代码
2025-08-27 16:55:53.918 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: float - 浮点数
2025-08-27 16:55:53.918 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: integer - 整数
2025-08-27 16:55:53.918 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: text_string - 文本字符串
2025-08-27 16:55:53.918 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: personnel_category_code - 人员类别代码
2025-08-27 16:55:53.918 | INFO     | src.modules.data_import.field_type_manager:__init__:41 | 字段类型管理器初始化完成，存储目录: E:\project\case\salary_changes\salary_changes\state\field_types
2025-08-27 16:55:54.127 | INFO     | src.gui.change_data_config_dialog:load_all_sheets:1300 | 成功加载 4 个工作表
2025-08-27 16:56:02.397 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1464 | 行5字段类型变更为personnel_category_code，示例数据更新为: 01
2025-08-27 16:56:02.409 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 16:56:10.252 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1464 | 行12字段类型变更为salary_float，示例数据更新为: 0.00
2025-08-27 16:56:10.267 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 16:56:13.307 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1464 | 行16字段类型变更为salary_float，示例数据更新为: 1,690.00
2025-08-27 16:56:13.322 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 16:56:17.791 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1464 | 行19字段类型变更为salary_float，示例数据更新为: 352.00
2025-08-27 16:56:17.805 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 16:56:20.255 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1464 | 行22字段类型变更为salary_float，示例数据更新为: 2,097.00
2025-08-27 16:56:20.269 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 16:56:37.597 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:777 | 🔍 开始扫描所有工作表的配置状态...
2025-08-27 16:56:37.607 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:790 | 🔧 修复工作表名称：从 'unknown' 更正为 '离休人员工资表'
2025-08-27 16:56:37.608 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:797 | ✅ 收集到当前工作表 '离休人员工资表' 配置：23 个字段
2025-08-27 16:56:37.608 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:822 | 🎯 配置收集完成，共找到 1 个已配置的工作表：
2025-08-27 16:56:37.608 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:825 |    - 离休人员工资表: 23 个字段
2025-08-27 16:56:43.397 | INFO     | src.gui.change_data_config_dialog:save_configuration:900 | 🔧 [缓存更新] 工作表 '离休人员工资表' 配置已更新到缓存
2025-08-27 16:56:44.707 | INFO     | src.gui.change_data_config_dialog:save_configuration:920 | 多工作表配置另存成功: E:\project\case\salary_changes\salary_changes\state\change_data_configs\user_configs\tt1.json, 包含 1 个工作表
2025-08-27 16:56:56.732 | INFO     | src.gui.change_data_config_dialog:on_sheet_changed:1321 | 切换到工作表: 退休人员工资表
2025-08-27 16:57:01.728 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1464 | 行4字段类型变更为text_string，示例数据更新为: 退休
2025-08-27 16:57:01.748 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 16:57:03.937 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1464 | 行5字段类型变更为personnel_category_code，示例数据更新为: 05
2025-08-27 16:57:03.959 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 16:57:07.837 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1464 | 行10字段类型变更为salary_float，示例数据更新为: 5280.50
2025-08-27 16:57:07.855 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 16:57:12.409 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1464 | 行13字段类型变更为salary_float，示例数据更新为: 0.00
2025-08-27 16:57:12.429 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 16:57:14.431 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1464 | 行14字段类型变更为salary_float，示例数据更新为: 148.00
2025-08-27 16:57:14.447 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 16:57:19.947 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1464 | 行22字段类型变更为salary_float，示例数据更新为: 5280.50
2025-08-27 16:57:19.968 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 16:57:22.376 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1464 | 行23字段类型变更为salary_float，示例数据更新为: 5280.50
2025-08-27 16:57:22.388 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 16:57:24.700 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1464 | 行25字段类型变更为salary_float，示例数据更新为: 5280.50
2025-08-27 16:57:24.719 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 16:57:40.857 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:777 | 🔍 开始扫描所有工作表的配置状态...
2025-08-27 16:57:40.857 | INFO     | src.gui.change_data_config_dialog:_save_current_sheet_config_to_cache:1333 | 🔧 [关键修复] 当前工作表 '退休人员工资表' 配置已保存到缓存
2025-08-27 16:57:40.857 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:797 | ✅ 收集到当前工作表 '退休人员工资表' 配置：27 个字段
2025-08-27 16:57:40.857 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:806 | ✅ 收集到工作表 '离休人员工资表' 配置：23 个字段
2025-08-27 16:57:40.857 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:822 | 🎯 配置收集完成，共找到 2 个已配置的工作表：
2025-08-27 16:57:40.857 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:825 |    - 退休人员工资表: 27 个字段
2025-08-27 16:57:40.857 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:825 |    - 离休人员工资表: 23 个字段
2025-08-27 16:57:46.658 | INFO     | src.gui.change_data_config_dialog:save_configuration:900 | 🔧 [缓存更新] 工作表 '退休人员工资表' 配置已更新到缓存
2025-08-27 16:57:46.659 | INFO     | src.gui.change_data_config_dialog:save_configuration:900 | 🔧 [缓存更新] 工作表 '离休人员工资表' 配置已更新到缓存
2025-08-27 16:58:16.628 | INFO     | src.gui.change_data_config_dialog:save_configuration:920 | 多工作表配置另存成功: E:\project\case\salary_changes\salary_changes\state\change_data_configs\user_configs\tt1.json, 包含 2 个工作表
2025-08-27 16:58:20.664 | INFO     | src.gui.change_data_config_dialog:on_sheet_changed:1312 | 已保存工作表 '退休人员工资表' 的配置
2025-08-27 16:58:20.755 | INFO     | src.gui.change_data_config_dialog:on_sheet_changed:1321 | 切换到工作表: 全部在职人员工资表
2025-08-27 16:59:02.457 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1464 | 行5字段类型变更为personnel_category_code，示例数据更新为: 01
2025-08-27 16:59:02.471 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 17:00:09.434 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1464 | 行16字段类型变更为salary_float，示例数据更新为: 1,690.00
2025-08-27 17:00:09.448 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 17:00:13.626 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1464 | 行19字段类型变更为salary_float，示例数据更新为: 352.00
2025-08-27 17:00:13.640 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 17:00:16.226 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1464 | 行22字段类型变更为salary_float，示例数据更新为: 2,097.00
2025-08-27 17:00:16.239 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 17:00:49.587 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:777 | 🔍 开始扫描所有工作表的配置状态...
2025-08-27 17:00:49.603 | INFO     | src.gui.change_data_config_dialog:_save_current_sheet_config_to_cache:1333 | 🔧 [关键修复] 当前工作表 '全部在职人员工资表' 配置已保存到缓存
2025-08-27 17:00:49.604 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:797 | ✅ 收集到当前工作表 '全部在职人员工资表' 配置：23 个字段
2025-08-27 17:00:49.604 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:806 | ✅ 收集到工作表 '离休人员工资表' 配置：23 个字段
2025-08-27 17:00:49.605 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:806 | ✅ 收集到工作表 '退休人员工资表' 配置：27 个字段
2025-08-27 17:00:49.605 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:822 | 🎯 配置收集完成，共找到 3 个已配置的工作表：
2025-08-27 17:00:49.606 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:825 |    - 全部在职人员工资表: 23 个字段
2025-08-27 17:00:49.606 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:825 |    - 离休人员工资表: 23 个字段
2025-08-27 17:00:49.606 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:825 |    - 退休人员工资表: 27 个字段
2025-08-27 17:00:55.781 | INFO     | src.gui.change_data_config_dialog:save_configuration:900 | 🔧 [缓存更新] 工作表 '全部在职人员工资表' 配置已更新到缓存
2025-08-27 17:00:55.781 | INFO     | src.gui.change_data_config_dialog:save_configuration:900 | 🔧 [缓存更新] 工作表 '离休人员工资表' 配置已更新到缓存
2025-08-27 17:00:55.782 | INFO     | src.gui.change_data_config_dialog:save_configuration:900 | 🔧 [缓存更新] 工作表 '退休人员工资表' 配置已更新到缓存
2025-08-27 17:00:59.061 | INFO     | src.gui.change_data_config_dialog:save_configuration:920 | 多工作表配置另存成功: E:\project\case\salary_changes\salary_changes\state\change_data_configs\user_configs\tt1.json, 包含 3 个工作表
2025-08-27 17:01:29.530 | INFO     | src.gui.change_data_config_dialog:on_sheet_changed:1312 | 已保存工作表 '全部在职人员工资表' 的配置
2025-08-27 17:01:29.602 | INFO     | src.gui.change_data_config_dialog:on_sheet_changed:1321 | 切换到工作表: A岗职工
2025-08-27 17:01:46.577 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1464 | 行6字段类型变更为personnel_category_code，示例数据更新为: 01
2025-08-27 17:01:46.592 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 17:01:54.577 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1464 | 行12字段类型变更为employee_id_string，示例数据更新为: 20
2025-08-27 17:01:54.592 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 17:01:57.363 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1464 | 行12字段类型变更为salary_float，示例数据更新为: 20.00
2025-08-27 17:01:57.376 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 17:02:00.542 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1464 | 行14字段类型变更为salary_float，示例数据更新为: 5280.50
2025-08-27 17:02:00.558 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 17:02:02.810 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1464 | 行16字段类型变更为salary_float，示例数据更新为: 5280.50
2025-08-27 17:02:02.810 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 17:02:08.741 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1464 | 行17字段类型变更为salary_float，示例数据更新为: 2,000.00
2025-08-27 17:02:08.759 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 17:02:11.157 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1464 | 行19字段类型变更为salary_float，示例数据更新为: 1,487.00
2025-08-27 17:02:11.184 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 17:02:15.620 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1464 | 行21字段类型变更为salary_float，示例数据更新为: 5280.50
2025-08-27 17:02:15.627 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 17:02:36.258 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:777 | 🔍 开始扫描所有工作表的配置状态...
2025-08-27 17:02:36.258 | INFO     | src.gui.change_data_config_dialog:_save_current_sheet_config_to_cache:1333 | 🔧 [关键修复] 当前工作表 'A岗职工' 配置已保存到缓存
2025-08-27 17:02:36.258 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:797 | ✅ 收集到当前工作表 'A岗职工' 配置：21 个字段
2025-08-27 17:02:36.258 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:806 | ✅ 收集到工作表 '离休人员工资表' 配置：23 个字段
2025-08-27 17:02:36.258 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:806 | ✅ 收集到工作表 '退休人员工资表' 配置：27 个字段
2025-08-27 17:02:36.258 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:806 | ✅ 收集到工作表 '全部在职人员工资表' 配置：23 个字段
2025-08-27 17:02:36.258 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:822 | 🎯 配置收集完成，共找到 4 个已配置的工作表：
2025-08-27 17:02:36.258 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:825 |    - A岗职工: 21 个字段
2025-08-27 17:02:36.258 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:825 |    - 离休人员工资表: 23 个字段
2025-08-27 17:02:36.258 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:825 |    - 退休人员工资表: 27 个字段
2025-08-27 17:02:36.258 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:825 |    - 全部在职人员工资表: 23 个字段
2025-08-27 17:02:41.539 | INFO     | src.gui.change_data_config_dialog:save_configuration:900 | 🔧 [缓存更新] 工作表 'A岗职工' 配置已更新到缓存
2025-08-27 17:02:41.539 | INFO     | src.gui.change_data_config_dialog:save_configuration:900 | 🔧 [缓存更新] 工作表 '离休人员工资表' 配置已更新到缓存
2025-08-27 17:02:41.539 | INFO     | src.gui.change_data_config_dialog:save_configuration:900 | 🔧 [缓存更新] 工作表 '退休人员工资表' 配置已更新到缓存
2025-08-27 17:02:41.539 | INFO     | src.gui.change_data_config_dialog:save_configuration:900 | 🔧 [缓存更新] 工作表 '全部在职人员工资表' 配置已更新到缓存
2025-08-27 17:02:47.190 | INFO     | src.gui.change_data_config_dialog:save_configuration:920 | 多工作表配置另存成功: E:\project\case\salary_changes\salary_changes\state\change_data_configs\user_configs\tt1.json, 包含 4 个工作表
2025-08-27 17:08:13.661 | INFO     | __main__:main:519 | 应用程序正常退出
2025-08-27 18:43:49.714 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-08-27 18:43:49.717 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-08-27 18:43:49.723 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-08-27 18:43:49.723 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-08-27 18:43:49.724 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-08-27 18:43:49.725 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-27 18:43:54.862 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-08-27 18:43:54.862 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-08-27 18:43:54.868 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-08-27 18:43:54.868 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-08-27 18:43:54.869 | INFO     | __main__:setup_app_logging:423 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-08-27 18:43:54.869 | INFO     | __main__:main:487 | 初始化核心管理器...
2025-08-27 18:43:54.870 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-27 18:43:54.871 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-27 18:43:54.872 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-27 18:43:54.887 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-27 18:43:54.888 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-27 18:43:54.921 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-27 18:43:54.922 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-08-27 18:43:54.927 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-27 18:43:54.932 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-27 18:43:54.933 | INFO     | __main__:main:492 | 核心管理器初始化完成。
2025-08-27 18:43:54.961 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-08-27 18:43:54.962 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-08-27 18:43:54.966 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-27 18:43:54.967 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-08-27 18:43:54.971 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-08-27 18:43:54.971 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-08-27 18:43:54.972 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-08-27 18:43:54.973 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:11817 | 🔧 [P2-3] 错误恢复策略注册完成
2025-08-27 18:43:54.974 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-27 18:43:54.987 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:11672 | 🔧 [P2-3] 错误处理机制设置完成
2025-08-27 18:43:54.987 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:11710 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-08-27 18:43:55.178 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-08-27 18:43:55.178 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-08-27 18:43:55.184 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-27 18:43:55.186 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-08-27 18:43:55.187 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-27 18:43:55.188 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-27 18:43:55.188 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-27 18:43:55.189 | INFO     | src.core.field_mapping_manager:_load_config:89 | 🔧 [P3优化] 字段映射配置加载成功
2025-08-27 18:43:55.192 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-08-27 18:43:55.206 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-27 18:43:55.207 | INFO     | src.modules.format_management.field_registry:__init__:95 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-08-27 18:43:55.208 | INFO     | src.core.unified_mapping_service:__init__:45 | UnifiedMappingService 初始化完成
2025-08-27 18:43:55.209 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-08-27 18:43:55.210 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-08-27 18:43:55.211 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-27 18:43:55.212 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-27 18:43:55.213 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-08-27 18:43:55.220 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 35.9ms
2025-08-27 18:43:55.245 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-08-27 18:43:55.246 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-08-27 18:43:55.252 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-08-27 18:43:55.253 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-08-27 18:43:55.253 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-08-27 18:43:55.254 | INFO     | src.gui.prototype.prototype_main_window:__init__:3650 | 🚀 性能管理器已集成
2025-08-27 18:43:55.256 | INFO     | src.gui.prototype.prototype_main_window:__init__:3652 | ✅ 新架构集成成功！
2025-08-27 18:43:55.263 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3765 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-08-27 18:43:55.270 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3730 | ✅ 新架构事件监听器设置完成
2025-08-27 18:43:55.271 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-08-27 18:43:55.275 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-08-27 18:43:55.275 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-08-27 18:43:55.972 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2726 | 菜单栏创建完成
2025-08-27 18:43:55.973 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-27 18:43:55.979 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: user_preferences.json
2025-08-27 18:43:55.980 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-08-27 18:43:55.988 | INFO     | src.gui.prototype.prototype_main_window:__init__:2701 | 菜单栏管理器初始化完成
2025-08-27 18:43:55.992 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-08-27 18:43:55.992 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5458 | 管理器设置完成，包含增强版表头管理器
2025-08-27 18:43:55.993 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5463 | 🔧 开始应用窗口级Material Design样式...
2025-08-27 18:43:55.994 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-08-27 18:43:56.001 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-08-27 18:43:56.007 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5470 | ✅ 窗口级样式应用成功
2025-08-27 18:43:56.009 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5511 | ✅ 响应式样式监听设置完成
2025-08-27 18:43:56.030 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-27 18:43:56.031 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-27 18:43:56.032 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-27 18:43:56.052 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-27 18:43:56.083 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-27 18:43:56.103 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-27 18:43:56.110 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1835 | 🏠 使用示例数据结构加载导航（首次启动或暂无数据时显示）
2025-08-27 18:43:56.112 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:2037 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-27 18:43:56.112 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-27 18:43:56.112 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:924 | 恢复导航状态: 0个展开项
2025-08-27 18:43:56.112 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月', '工资表 > 2025年 > 05月 > 全部在职人员']
2025-08-27 18:43:56.135 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-27 18:43:56.137 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-27 18:43:56.141 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-27 18:43:56.147 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-27 18:43:56.150 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:2037 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-27 18:43:56.150 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-27 18:43:56.151 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-27 18:43:56.153 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-27 18:43:56.153 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 18:43:56.155 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1601 | 🔧 [P1-2修复] 检查表状态失败，1.0s后重试...
2025-08-27 18:43:56.162 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1405 | 🔧 [P1-2修复] 未找到最新工资数据路径（可能是首次启动）
2025-08-27 18:43:56.497 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-08-27 18:43:56.500 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2142 | 🚨 [架构修复] 启用模型数据同步机制
2025-08-27 18:43:56.506 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1366 | 快捷键注册完成: 18/18 个
2025-08-27 18:43:56.506 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1809 | 拖拽排序管理器初始化完成
2025-08-27 18:43:56.559 | INFO     | src.modules.data_management.data_flow_validator:__init__:85 | 🔧 [数据验证器] 初始化完成，验证级别: lenient
2025-08-27 18:43:56.560 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-08-27 18:43:56.564 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-08-27 18:43:56.566 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2195 | 🔧 [P0-紧急修复] 数据流验证器初始化成功（宽松模式）
2025-08-27 18:43:56.568 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-08-27 18:43:56.569 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-27 18:43:56.569 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-27 18:43:56.570 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2247 | 🎯 [统一格式管理] 使用统一格式管理器
2025-08-27 18:43:56.585 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:370 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-27 18:43:56.586 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-08-27 18:43:56.587 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2294 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-08-27 18:43:56.588 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1553 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-08-27 18:43:56.589 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1554 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-08-27 18:43:56.589 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1555 | 🔧 [列宽保存修复] 配置文件存在: True
2025-08-27 18:43:56.591 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1556 | 🔧 [列宽保存修复] 父目录存在: True
2025-08-27 18:43:56.601 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1557 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-08-27 18:43:56.602 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2301 | 列宽管理器初始化完成
2025-08-27 18:43:56.606 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2428 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-08-27 18:43:56.609 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2315 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-08-27 18:43:56.610 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-27 18:43:56.621 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-27 18:43:56.622 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-27 18:43:56.632 | INFO     | src.gui.prototype.widgets.pagination_state_manager:__init__:78 | 🔧 [P1优化] 分页状态管理器初始化完成
2025-08-27 18:43:56.643 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 0
2025-08-27 18:43:56.645 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2838 | 🔧 [P1优化] 表切换操作: None -> default_table
2025-08-27 18:43:56.646 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-27 18:43:56.657 | INFO     | src.gui.prototype.widgets.table_header_cache:__init__:67 | 🔧 [P1优化] 表头缓存管理器初始化完成，最大缓存: 100
2025-08-27 18:43:56.676 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2890 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-27 18:43:56.679 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-27 18:43:56.694 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-27 18:43:56.697 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-27 18:43:56.699 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-27 18:43:56.699 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-27 18:43:56.700 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-27 18:43:56.704 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-08-27 18:43:56.706 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 81.1ms
2025-08-27 18:43:56.714 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-27 18:43:56.715 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-27 18:43:56.721 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-27 18:43:56.722 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-27 18:43:56.723 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-27 18:43:56.742 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-08-27 18:43:56.755 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-08-27 18:43:56.756 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-08-27 18:43:56.801 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:594 | 控制面板按钮信号连接完成
2025-08-27 18:43:56.858 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-27 18:43:56.859 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-27 18:43:56.862 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5420 | 快捷键设置完成
2025-08-27 18:43:56.864 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5377 | 主窗口UI设置完成。
2025-08-27 18:43:56.865 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5614 | 🔧 [全局排序] 全局排序开关连接成功
2025-08-27 18:43:56.865 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5646 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-08-27 18:43:56.866 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5658 | ✅ 已连接分页刷新信号到主窗口
2025-08-27 18:43:56.867 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5659 | ✅ 已连接分页组件事件到新架构
2025-08-27 18:43:56.869 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5670 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-08-27 18:43:56.869 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5673 | 信号连接设置完成
2025-08-27 18:43:56.871 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6849 | 🔧 [P1-2修复] 发现 2 个表的配置
2025-08-27 18:43:56.879 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: salary_data_2025_07_active_employees, 10个字段
2025-08-27 18:43:56.879 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: active_employees, 5个字段
2025-08-27 18:43:56.880 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6859 | ✅ [P1-2修复] 已加载字段映射信息，共2个表的映射
2025-08-27 18:43:56.889 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-27 18:43:56.892 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-27 18:43:56.893 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-27 18:43:56.894 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-27 18:43:56.894 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-27 18:43:56.895 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 22
2025-08-27 18:43:56.896 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-27 18:43:56.897 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-27 18:43:56.897 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-27 18:43:56.898 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-27 18:43:56.899 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 6.1ms
2025-08-27 18:43:56.900 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-27 18:43:56.911 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-27 18:43:56.913 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-27 18:43:56.914 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-27 18:43:56.915 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-27 18:43:56.916 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-27 18:43:56.918 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8551 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-08-27 18:43:56.918 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-27 18:43:56.919 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-27 18:43:56.927 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-27 18:43:56.927 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-27 18:43:56.928 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-27 18:43:56.929 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-27 18:43:56.932 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-27 18:43:56.940 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-27 18:43:56.946 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-27 18:43:56.947 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 20.7ms
2025-08-27 18:43:56.948 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-27 18:43:56.950 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-27 18:43:56.951 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-27 18:43:56.952 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-27 18:43:56.952 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-27 18:43:56.954 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8569 | 已显示标准空表格，表头数量: 22
2025-08-27 18:43:56.962 | INFO     | src.gui.prototype.prototype_main_window:__init__:3704 | 原型主窗口初始化完成
2025-08-27 18:43:57.136 | INFO     | __main__:main:514 | 应用程序启动成功
2025-08-27 18:43:57.151 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-27 18:43:57.173 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1474 | 执行延迟的自动选择最新数据...
2025-08-27 18:43:57.175 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 18:43:57.181 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1497 | 延迟自动选择最新数据失败，可能没有可用数据
2025-08-27 18:43:57.248 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-27 18:43:57.254 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-27 18:43:57.308 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 18:43:57.311 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-08-27 18:43:57.314 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2079 | MainWorkspaceArea 响应式适配: sm
2025-08-27 18:43:57.823 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9453 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet']
2025-08-27 18:43:57.824 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9363 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-27 18:43:57.827 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9377 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet']
2025-08-27 18:43:57.827 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:9911 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet']
2025-08-27 18:43:57.842 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9383 | 🔧 [P0-1] 智能显示亮度修复完成
2025-08-27 18:43:58.182 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-27 18:43:58.182 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 18:43:58.186 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1612 | 🔧 [P1-2修复] 3次重试已用尽，可能数据导入尚未完成
2025-08-27 18:44:05.216 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:654 | 数据导入功能被触发，发出 import_requested 信号。
2025-08-27 18:44:05.217 | INFO     | src.gui.prototype.prototype_main_window:_get_suggested_target_path:8374 | 检测到当前在工资表TAB，生成工资表默认路径
2025-08-27 18:44:05.222 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5906 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 08月 > 全部在职人员。打开导入对话框。
2025-08-27 18:44:05.225 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-08-27 18:44:05.226 | INFO     | src.modules.data_import.change_data_config_manager:__init__:43 | 配置管理器初始化完成，配置目录: C:\test\salary_changes\salary_changes\state\change_data_configs
2025-08-27 18:44:05.228 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:78 | 多Sheet导入器初始化完成
2025-08-27 18:44:05.229 | INFO     | src.gui.widgets.target_selection_widget:_load_available_options:250 | 成功加载导航配置
2025-08-27 18:44:05.252 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-27 18:44:05.313 | INFO     | src.gui.main_dialogs:_get_template_fields:2010 | 🔧 [P3-修复] ConfigSyncManager未初始化，使用默认字段模板
2025-08-27 18:44:05.314 | INFO     | src.gui.main_dialogs:_init_field_mapping:1997 | 🔧 [P3-修复] 未找到字段模板，使用内置默认字段列表
2025-08-27 18:44:05.386 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-08-27 18:44:05.387 | INFO     | src.modules.data_import.import_defaults_manager:get_smart_defaults_for_category:244 | 检测到工资表导入，设置table_template为salary_data
2025-08-27 18:44:05.390 | INFO     | src.gui.main_dialogs:_apply_default_settings:2616 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'separate_tables', 'table_template': 'salary_data'}
2025-08-27 18:44:05.392 | INFO     | src.gui.main_dialogs:_setup_tooltips:2989 | 工具提示设置完成
2025-08-27 18:44:05.394 | INFO     | src.gui.main_dialogs:_setup_shortcuts:3028 | 快捷键设置完成
2025-08-27 18:44:05.394 | INFO     | src.gui.main_dialogs:__init__:82 | 数据导入对话框初始化完成。
2025-08-27 18:44:05.395 | INFO     | src.modules.data_import.multi_sheet_importer:set_config_sync_manager:87 | 🔧 [修复] ConfigSyncManager已设置到MultiSheetImporter
2025-08-27 18:44:05.396 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5917 | 🔧 [P0-修复] ConfigSyncManager已设置到数据导入对话框
2025-08-27 18:44:07.345 | INFO     | src.gui.main_dialogs:_on_target_changed:2522 | 目标位置已更新: 异动人员表 > 2025年 > 08月 > 全部在职人员
2025-08-27 18:44:07.346 | INFO     | src.gui.main_dialogs:_on_change_mode_changed:2284 | 🔧 [P2-1修复] 异动表处理模式已切换为: 动态处理（保持原始）
2025-08-27 18:44:07.348 | INFO     | src.gui.main_dialogs:_on_target_changed:2541 | 数据期间自动同步为: 2025-08
2025-08-27 18:44:07.350 | INFO     | src.gui.main_dialogs:_on_target_changed:2561 | 数据描述自动生成: 2025年08月全部在职人员工资
2025-08-27 18:44:09.665 | INFO     | src.gui.main_dialogs:_on_target_changed:2522 | 目标位置已更新: 异动人员表 > 2025年 > 12月 > 全部在职人员
2025-08-27 18:44:09.665 | INFO     | src.gui.main_dialogs:_on_target_changed:2541 | 数据期间自动同步为: 2025-12
2025-08-27 18:44:09.668 | INFO     | src.gui.main_dialogs:_on_target_changed:2561 | 数据描述自动生成: 2025年12月全部在职人员工资
2025-08-27 18:44:16.099 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-27 18:44:17.353 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-08-27 18:44:17.354 | INFO     | src.gui.main_dialogs:_auto_select_sheet_by_category:2651 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-08-27 18:44:22.154 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-27 18:44:22.154 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-27 18:44:22.157 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-27 18:44:22.305 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 23列 (列过滤: 否)
2025-08-27 18:44:22.311 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-27 18:44:22.315 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-08-27 18:44:22.315 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1397行 x 23列
2025-08-27 18:44:22.339 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-27 18:44:22.344 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-08-27 18:44:22.347 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1396行 × 23列
2025-08-27 18:44:22.401 | INFO     | src.modules.data_import.change_data_config_manager:__init__:43 | 配置管理器初始化完成，配置目录: C:\test\salary_changes\salary_changes\state\change_data_configs
2025-08-27 18:44:22.402 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: number
2025-08-27 18:44:22.405 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: string
2025-08-27 18:44:22.405 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: date
2025-08-27 18:44:22.406 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: code
2025-08-27 18:44:22.407 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: custom
2025-08-27 18:44:22.407 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: salary_float - 工资金额
2025-08-27 18:44:22.408 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: employee_id_string - 工号
2025-08-27 18:44:22.409 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: name_string - 姓名
2025-08-27 18:44:22.411 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: date_string - 日期
2025-08-27 18:44:22.411 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: id_number_string - 身份证号
2025-08-27 18:44:22.412 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: code_string - 代码
2025-08-27 18:44:22.413 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: float - 浮点数
2025-08-27 18:44:22.440 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: integer - 整数
2025-08-27 18:44:22.441 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: text_string - 文本字符串
2025-08-27 18:44:22.442 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: personnel_category_code - 人员类别代码
2025-08-27 18:44:22.443 | INFO     | src.modules.data_import.field_type_manager:__init__:41 | 字段类型管理器初始化完成，存储目录: C:\test\salary_changes\salary_changes\state\field_types
2025-08-27 18:44:22.958 | INFO     | src.gui.change_data_config_dialog:load_all_sheets:1409 | 成功加载 4 个工作表
2025-08-27 18:44:53.089 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1573 | 行5字段类型变更为personnel_category_code，示例数据更新为: 01
2025-08-27 18:44:53.112 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 18:45:02.873 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1573 | 行12字段类型变更为salary_float，示例数据更新为: 0.00
2025-08-27 18:45:02.892 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 18:45:06.161 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1573 | 行16字段类型变更为salary_float，示例数据更新为: 1,690.00
2025-08-27 18:45:06.181 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 18:45:12.688 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1573 | 行19字段类型变更为salary_float，示例数据更新为: 352.00
2025-08-27 18:45:12.707 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 18:45:15.824 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1573 | 行22字段类型变更为salary_float，示例数据更新为: 2,097.00
2025-08-27 18:45:15.845 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 18:45:38.792 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:778 | 🔍 开始收集所有工作表的配置状态（包括未修改的表）...
2025-08-27 18:45:38.793 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:791 | 🔧 修复工作表名称：从 'unknown' 更正为 '离休人员工资表'
2025-08-27 18:45:38.797 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:798 | ✅ 收集到当前工作表 '离休人员工资表' 配置：23 个字段
2025-08-27 18:45:38.797 | INFO     | src.gui.change_data_config_dialog:_generate_default_config_for_sheet:898 | 为工作表 '退休人员工资表' 生成默认配置：27 个字段
2025-08-27 18:45:38.798 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:829 | 🆕 为未配置工作表 '退休人员工资表' 生成默认配置：27 个字段
2025-08-27 18:45:38.799 | INFO     | src.gui.change_data_config_dialog:_generate_default_config_for_sheet:898 | 为工作表 '全部在职人员工资表' 生成默认配置：23 个字段
2025-08-27 18:45:38.800 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:829 | 🆕 为未配置工作表 '全部在职人员工资表' 生成默认配置：23 个字段
2025-08-27 18:45:38.801 | INFO     | src.gui.change_data_config_dialog:_generate_default_config_for_sheet:898 | 为工作表 'A岗职工' 生成默认配置：21 个字段
2025-08-27 18:45:38.802 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:829 | 🆕 为未配置工作表 'A岗职工' 生成默认配置：21 个字段
2025-08-27 18:45:38.802 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:833 | 🎯 配置收集完成，共收集 4 个工作表的配置：
2025-08-27 18:45:38.803 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:836 |    - 离休人员工资表: 23 个字段
2025-08-27 18:45:38.804 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:836 |    - 退休人员工资表: 27 个字段
2025-08-27 18:45:38.811 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:836 |    - 全部在职人员工资表: 23 个字段
2025-08-27 18:45:38.812 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:836 |    - A岗职工: 21 个字段
2025-08-27 18:45:46.796 | INFO     | src.gui.change_data_config_dialog:save_configuration:1009 | 🔧 [缓存更新] 工作表 '离休人员工资表' 配置已更新到缓存
2025-08-27 18:45:46.797 | INFO     | src.gui.change_data_config_dialog:save_configuration:1009 | 🔧 [缓存更新] 工作表 '退休人员工资表' 配置已更新到缓存
2025-08-27 18:45:46.800 | INFO     | src.gui.change_data_config_dialog:save_configuration:1009 | 🔧 [缓存更新] 工作表 '全部在职人员工资表' 配置已更新到缓存
2025-08-27 18:45:46.800 | INFO     | src.gui.change_data_config_dialog:save_configuration:1009 | 🔧 [缓存更新] 工作表 'A岗职工' 配置已更新到缓存
2025-08-27 18:45:48.507 | INFO     | src.gui.change_data_config_dialog:save_configuration:1029 | 多工作表配置另存成功: C:\test\salary_changes\salary_changes\state\change_data_configs\user_configs\tt1.json, 包含 4 个工作表
2025-08-27 18:46:55.195 | INFO     | src.gui.change_data_config_dialog:on_sheet_changed:1430 | 切换到工作表: 退休人员工资表
2025-08-27 18:46:59.867 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1573 | 行4字段类型变更为text_string，示例数据更新为: 退休
2025-08-27 18:46:59.883 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 18:47:01.945 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1573 | 行5字段类型变更为personnel_category_code，示例数据更新为: 05
2025-08-27 18:47:01.977 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 18:47:05.601 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1573 | 行10字段类型变更为salary_float，示例数据更新为: 5280.50
2025-08-27 18:47:05.617 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 18:47:10.398 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1573 | 行13字段类型变更为salary_float，示例数据更新为: 0.00
2025-08-27 18:47:10.414 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 18:47:12.758 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1573 | 行14字段类型变更为salary_float，示例数据更新为: 148.00
2025-08-27 18:47:12.773 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 18:47:20.383 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1573 | 行22字段类型变更为salary_float，示例数据更新为: 5280.50
2025-08-27 18:47:20.398 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 18:47:23.336 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1573 | 行23字段类型变更为salary_float，示例数据更新为: 5280.50
2025-08-27 18:47:23.351 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 18:47:26.039 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1573 | 行25字段类型变更为salary_float，示例数据更新为: 5280.50
2025-08-27 18:47:26.070 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 18:47:49.257 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1573 | 行2字段类型变更为employee_id_string，示例数据更新为: 19709165
2025-08-27 18:47:49.272 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 18:48:03.022 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:778 | 🔍 开始收集所有工作表的配置状态（包括未修改的表）...
2025-08-27 18:48:03.022 | INFO     | src.gui.change_data_config_dialog:_save_current_sheet_config_to_cache:1442 | 🔧 [关键修复] 当前工作表 '退休人员工资表' 配置已保存到缓存
2025-08-27 18:48:03.022 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:798 | ✅ 收集到当前工作表 '退休人员工资表' 配置：27 个字段
2025-08-27 18:48:03.022 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:807 | ✅ 收集到工作表 '离休人员工资表' 配置：23 个字段
2025-08-27 18:48:03.022 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:807 | ✅ 收集到工作表 '全部在职人员工资表' 配置：23 个字段
2025-08-27 18:48:03.038 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:807 | ✅ 收集到工作表 'A岗职工' 配置：21 个字段
2025-08-27 18:48:03.038 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:833 | 🎯 配置收集完成，共收集 4 个工作表的配置：
2025-08-27 18:48:03.038 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:836 |    - 退休人员工资表: 27 个字段
2025-08-27 18:48:03.038 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:836 |    - 离休人员工资表: 23 个字段
2025-08-27 18:48:03.038 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:836 |    - 全部在职人员工资表: 23 个字段
2025-08-27 18:48:03.038 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:836 |    - A岗职工: 21 个字段
2025-08-27 18:48:09.397 | INFO     | src.gui.change_data_config_dialog:save_configuration:1009 | 🔧 [缓存更新] 工作表 '退休人员工资表' 配置已更新到缓存
2025-08-27 18:48:09.397 | INFO     | src.gui.change_data_config_dialog:save_configuration:1009 | 🔧 [缓存更新] 工作表 '离休人员工资表' 配置已更新到缓存
2025-08-27 18:48:09.413 | INFO     | src.gui.change_data_config_dialog:save_configuration:1009 | 🔧 [缓存更新] 工作表 '全部在职人员工资表' 配置已更新到缓存
2025-08-27 18:48:09.413 | INFO     | src.gui.change_data_config_dialog:save_configuration:1009 | 🔧 [缓存更新] 工作表 'A岗职工' 配置已更新到缓存
2025-08-27 18:48:12.409 | INFO     | src.gui.change_data_config_dialog:save_configuration:1029 | 多工作表配置另存成功: C:\test\salary_changes\salary_changes\state\change_data_configs\user_configs\tt1.json, 包含 4 个工作表
2025-08-27 18:49:06.660 | INFO     | src.gui.change_data_config_dialog:on_sheet_changed:1421 | 已保存工作表 '退休人员工资表' 的配置
2025-08-27 18:49:06.834 | INFO     | src.gui.change_data_config_dialog:on_sheet_changed:1430 | 切换到工作表: 全部在职人员工资表
2025-08-27 18:49:11.894 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1573 | 行22字段类型变更为salary_float，示例数据更新为: 2,097.00
2025-08-27 18:49:11.909 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 18:49:14.644 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1573 | 行19字段类型变更为salary_float，示例数据更新为: 352.00
2025-08-27 18:49:14.660 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 18:49:17.160 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1573 | 行16字段类型变更为salary_float，示例数据更新为: 1,690.00
2025-08-27 18:49:17.175 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 18:49:35.918 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1573 | 行5字段类型变更为personnel_category_code，示例数据更新为: 01
2025-08-27 18:49:35.933 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 18:49:50.308 | INFO     | src.gui.change_data_config_dialog:on_sheet_changed:1421 | 已保存工作表 '全部在职人员工资表' 的配置
2025-08-27 18:49:50.449 | INFO     | src.gui.change_data_config_dialog:on_sheet_changed:1430 | 切换到工作表: A岗职工
2025-08-27 18:50:03.230 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1573 | 行6字段类型变更为personnel_category_code，示例数据更新为: 01
2025-08-27 18:50:03.245 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 18:50:11.945 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1573 | 行12字段类型变更为salary_float，示例数据更新为: 20.00
2025-08-27 18:50:11.963 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 18:50:15.319 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1573 | 行14字段类型变更为salary_float，示例数据更新为: 5280.50
2025-08-27 18:50:15.334 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 18:50:18.709 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1573 | 行16字段类型变更为salary_float，示例数据更新为: 5280.50
2025-08-27 18:50:18.725 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 18:50:21.944 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1573 | 行17字段类型变更为salary_float，示例数据更新为: 2,000.00
2025-08-27 18:50:21.959 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 18:50:24.897 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1573 | 行19字段类型变更为salary_float，示例数据更新为: 1,487.00
2025-08-27 18:50:24.913 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 18:50:31.740 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1573 | 行21字段类型变更为salary_float，示例数据更新为: 5280.50
2025-08-27 18:50:31.756 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 18:51:37.236 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 18:51:41.205 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:778 | 🔍 开始收集所有工作表的配置状态（包括未修改的表）...
2025-08-27 18:51:41.205 | INFO     | src.gui.change_data_config_dialog:_save_current_sheet_config_to_cache:1442 | 🔧 [关键修复] 当前工作表 'A岗职工' 配置已保存到缓存
2025-08-27 18:51:41.205 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:798 | ✅ 收集到当前工作表 'A岗职工' 配置：21 个字段
2025-08-27 18:51:41.205 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:807 | ✅ 收集到工作表 '离休人员工资表' 配置：23 个字段
2025-08-27 18:51:41.205 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:807 | ✅ 收集到工作表 '退休人员工资表' 配置：27 个字段
2025-08-27 18:51:41.205 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:807 | ✅ 收集到工作表 '全部在职人员工资表' 配置：23 个字段
2025-08-27 18:51:41.205 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:833 | 🎯 配置收集完成，共收集 4 个工作表的配置：
2025-08-27 18:51:41.205 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:836 |    - A岗职工: 21 个字段
2025-08-27 18:51:41.221 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:836 |    - 离休人员工资表: 23 个字段
2025-08-27 18:51:41.221 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:836 |    - 退休人员工资表: 27 个字段
2025-08-27 18:51:41.221 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:836 |    - 全部在职人员工资表: 23 个字段
2025-08-27 18:51:46.939 | INFO     | src.gui.change_data_config_dialog:save_configuration:1009 | 🔧 [缓存更新] 工作表 'A岗职工' 配置已更新到缓存
2025-08-27 18:51:46.939 | INFO     | src.gui.change_data_config_dialog:save_configuration:1009 | 🔧 [缓存更新] 工作表 '离休人员工资表' 配置已更新到缓存
2025-08-27 18:51:46.939 | INFO     | src.gui.change_data_config_dialog:save_configuration:1009 | 🔧 [缓存更新] 工作表 '退休人员工资表' 配置已更新到缓存
2025-08-27 18:51:46.939 | INFO     | src.gui.change_data_config_dialog:save_configuration:1009 | 🔧 [缓存更新] 工作表 '全部在职人员工资表' 配置已更新到缓存
2025-08-27 18:51:49.093 | INFO     | src.gui.change_data_config_dialog:save_configuration:1029 | 多工作表配置另存成功: C:\test\salary_changes\salary_changes\state\change_data_configs\user_configs\tt1.json, 包含 4 个工作表
2025-08-27 19:00:09.720 | INFO     | __main__:main:519 | 应用程序正常退出
2025-08-27 19:08:29.951 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-08-27 19:08:29.951 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-08-27 19:08:29.952 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-08-27 19:08:29.952 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-08-27 19:08:29.953 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-08-27 19:08:29.953 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-27 19:08:33.090 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-08-27 19:08:33.090 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-08-27 19:08:33.091 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-08-27 19:08:33.093 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-08-27 19:08:33.093 | INFO     | __main__:setup_app_logging:423 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-08-27 19:08:33.093 | INFO     | __main__:main:487 | 初始化核心管理器...
2025-08-27 19:08:33.094 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-27 19:08:33.094 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-27 19:08:33.096 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-27 19:08:33.096 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-27 19:08:33.097 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-27 19:08:33.106 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-27 19:08:33.107 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-08-27 19:08:33.108 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-27 19:08:33.113 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-27 19:08:33.114 | INFO     | __main__:main:492 | 核心管理器初始化完成。
2025-08-27 19:08:33.125 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-08-27 19:08:33.126 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-08-27 19:08:33.127 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-27 19:08:33.128 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-08-27 19:08:33.129 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-08-27 19:08:33.130 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-08-27 19:08:33.131 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-08-27 19:08:33.136 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:11817 | 🔧 [P2-3] 错误恢复策略注册完成
2025-08-27 19:08:33.137 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-27 19:08:33.138 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:11672 | 🔧 [P2-3] 错误处理机制设置完成
2025-08-27 19:08:33.139 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:11710 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-08-27 19:08:33.351 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-08-27 19:08:33.351 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-08-27 19:08:33.356 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-27 19:08:33.358 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-08-27 19:08:33.359 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-27 19:08:33.360 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-27 19:08:33.360 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-27 19:08:33.362 | INFO     | src.core.field_mapping_manager:_load_config:89 | 🔧 [P3优化] 字段映射配置加载成功
2025-08-27 19:08:33.365 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-08-27 19:08:33.368 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-27 19:08:33.370 | INFO     | src.modules.format_management.field_registry:__init__:95 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-08-27 19:08:33.370 | INFO     | src.core.unified_mapping_service:__init__:45 | UnifiedMappingService 初始化完成
2025-08-27 19:08:33.371 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-08-27 19:08:33.372 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-08-27 19:08:33.372 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-27 19:08:33.373 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-27 19:08:33.374 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-08-27 19:08:33.375 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 19.3ms
2025-08-27 19:08:33.393 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-08-27 19:08:33.394 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-08-27 19:08:33.398 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-08-27 19:08:33.399 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-08-27 19:08:33.400 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-08-27 19:08:33.401 | INFO     | src.gui.prototype.prototype_main_window:__init__:3650 | 🚀 性能管理器已集成
2025-08-27 19:08:33.402 | INFO     | src.gui.prototype.prototype_main_window:__init__:3652 | ✅ 新架构集成成功！
2025-08-27 19:08:33.403 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3765 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-08-27 19:08:33.403 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3730 | ✅ 新架构事件监听器设置完成
2025-08-27 19:08:33.405 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-08-27 19:08:33.406 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-08-27 19:08:33.412 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-08-27 19:08:33.666 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2726 | 菜单栏创建完成
2025-08-27 19:08:33.667 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-27 19:08:33.670 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: user_preferences.json
2025-08-27 19:08:33.671 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-08-27 19:08:33.674 | INFO     | src.gui.prototype.prototype_main_window:__init__:2701 | 菜单栏管理器初始化完成
2025-08-27 19:08:33.674 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-08-27 19:08:33.675 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5458 | 管理器设置完成，包含增强版表头管理器
2025-08-27 19:08:33.676 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5463 | 🔧 开始应用窗口级Material Design样式...
2025-08-27 19:08:33.677 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-08-27 19:08:33.679 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-08-27 19:08:33.682 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5470 | ✅ 窗口级样式应用成功
2025-08-27 19:08:33.684 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5511 | ✅ 响应式样式监听设置完成
2025-08-27 19:08:33.687 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-27 19:08:33.688 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-27 19:08:33.688 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-27 19:08:33.691 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-27 19:08:33.705 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-27 19:08:33.715 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-27 19:08:33.721 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1835 | 🏠 使用示例数据结构加载导航（首次启动或暂无数据时显示）
2025-08-27 19:08:33.725 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:2037 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-27 19:08:33.726 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-27 19:08:33.727 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:924 | 恢复导航状态: 0个展开项
2025-08-27 19:08:33.729 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月', '工资表 > 2025年 > 05月 > 全部在职人员']
2025-08-27 19:08:33.743 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-27 19:08:33.748 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-27 19:08:33.751 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-27 19:08:33.756 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-27 19:08:33.772 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:2037 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-27 19:08:33.773 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-27 19:08:33.775 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-27 19:08:33.776 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-27 19:08:33.777 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 19:08:33.779 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1601 | 🔧 [P1-2修复] 检查表状态失败，1.0s后重试...
2025-08-27 19:08:33.787 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1405 | 🔧 [P1-2修复] 未找到最新工资数据路径（可能是首次启动）
2025-08-27 19:08:34.034 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-08-27 19:08:34.036 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2142 | 🚨 [架构修复] 启用模型数据同步机制
2025-08-27 19:08:34.041 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1366 | 快捷键注册完成: 18/18 个
2025-08-27 19:08:34.042 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1809 | 拖拽排序管理器初始化完成
2025-08-27 19:08:34.067 | INFO     | src.modules.data_management.data_flow_validator:__init__:85 | 🔧 [数据验证器] 初始化完成，验证级别: lenient
2025-08-27 19:08:34.068 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-08-27 19:08:34.071 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-08-27 19:08:34.073 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2195 | 🔧 [P0-紧急修复] 数据流验证器初始化成功（宽松模式）
2025-08-27 19:08:34.074 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-08-27 19:08:34.074 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-27 19:08:34.075 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-27 19:08:34.076 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2247 | 🎯 [统一格式管理] 使用统一格式管理器
2025-08-27 19:08:34.094 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:370 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-27 19:08:34.095 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-08-27 19:08:34.099 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2294 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-08-27 19:08:34.102 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1553 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-08-27 19:08:34.102 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1554 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-08-27 19:08:34.103 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1555 | 🔧 [列宽保存修复] 配置文件存在: True
2025-08-27 19:08:34.104 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1556 | 🔧 [列宽保存修复] 父目录存在: True
2025-08-27 19:08:34.104 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1557 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-08-27 19:08:34.105 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2301 | 列宽管理器初始化完成
2025-08-27 19:08:34.106 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2428 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-08-27 19:08:34.109 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2315 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-08-27 19:08:34.115 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-27 19:08:34.117 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-27 19:08:34.117 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-27 19:08:34.134 | INFO     | src.gui.prototype.widgets.pagination_state_manager:__init__:78 | 🔧 [P1优化] 分页状态管理器初始化完成
2025-08-27 19:08:34.135 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 0
2025-08-27 19:08:34.135 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2838 | 🔧 [P1优化] 表切换操作: None -> default_table
2025-08-27 19:08:34.136 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-27 19:08:34.143 | INFO     | src.gui.prototype.widgets.table_header_cache:__init__:67 | 🔧 [P1优化] 表头缓存管理器初始化完成，最大缓存: 100
2025-08-27 19:08:34.143 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2890 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-27 19:08:34.145 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-27 19:08:34.161 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-27 19:08:34.172 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-27 19:08:34.173 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-27 19:08:34.174 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-27 19:08:34.175 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-27 19:08:34.177 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-08-27 19:08:34.179 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 58.8ms
2025-08-27 19:08:34.203 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-27 19:08:34.205 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-27 19:08:34.209 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-27 19:08:34.210 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-27 19:08:34.211 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-27 19:08:34.237 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-08-27 19:08:34.249 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-08-27 19:08:34.252 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-08-27 19:08:34.294 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:594 | 控制面板按钮信号连接完成
2025-08-27 19:08:34.351 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-27 19:08:34.352 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-27 19:08:34.355 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5420 | 快捷键设置完成
2025-08-27 19:08:34.355 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5377 | 主窗口UI设置完成。
2025-08-27 19:08:34.356 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5614 | 🔧 [全局排序] 全局排序开关连接成功
2025-08-27 19:08:34.357 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5646 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-08-27 19:08:34.358 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5658 | ✅ 已连接分页刷新信号到主窗口
2025-08-27 19:08:34.359 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5659 | ✅ 已连接分页组件事件到新架构
2025-08-27 19:08:34.363 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5670 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-08-27 19:08:34.369 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5673 | 信号连接设置完成
2025-08-27 19:08:34.370 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6849 | 🔧 [P1-2修复] 发现 2 个表的配置
2025-08-27 19:08:34.371 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: salary_data_2025_07_active_employees, 10个字段
2025-08-27 19:08:34.371 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: active_employees, 5个字段
2025-08-27 19:08:34.372 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6859 | ✅ [P1-2修复] 已加载字段映射信息，共2个表的映射
2025-08-27 19:08:34.381 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-27 19:08:34.384 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-27 19:08:34.385 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-27 19:08:34.386 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-27 19:08:34.388 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-27 19:08:34.388 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 22
2025-08-27 19:08:34.389 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-27 19:08:34.389 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-27 19:08:34.390 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-27 19:08:34.391 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-27 19:08:34.392 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 5.6ms
2025-08-27 19:08:34.393 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-27 19:08:34.404 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-27 19:08:34.405 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-27 19:08:34.406 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-27 19:08:34.407 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-27 19:08:34.408 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-27 19:08:34.409 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8551 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-08-27 19:08:34.410 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-27 19:08:34.411 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-27 19:08:34.412 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-27 19:08:34.418 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-27 19:08:34.419 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-27 19:08:34.420 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-27 19:08:34.421 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-27 19:08:34.422 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-27 19:08:34.423 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-27 19:08:34.423 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 5.8ms
2025-08-27 19:08:34.424 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-27 19:08:34.425 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-27 19:08:34.501 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-27 19:08:34.504 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-27 19:08:34.504 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-27 19:08:34.506 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8569 | 已显示标准空表格，表头数量: 22
2025-08-27 19:08:34.506 | INFO     | src.gui.prototype.prototype_main_window:__init__:3704 | 原型主窗口初始化完成
2025-08-27 19:08:34.619 | INFO     | __main__:main:514 | 应用程序启动成功
2025-08-27 19:08:34.627 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-27 19:08:34.631 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-27 19:08:34.636 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1474 | 执行延迟的自动选择最新数据...
2025-08-27 19:08:34.637 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 19:08:34.638 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1497 | 延迟自动选择最新数据失败，可能没有可用数据
2025-08-27 19:08:34.639 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-27 19:08:34.738 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-08-27 19:08:34.740 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2079 | MainWorkspaceArea 响应式适配: sm
2025-08-27 19:08:34.799 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 19:08:35.248 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9453 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet']
2025-08-27 19:08:35.250 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9363 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-27 19:08:35.254 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9377 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet']
2025-08-27 19:08:35.254 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:9911 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet']
2025-08-27 19:08:35.270 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9383 | 🔧 [P0-1] 智能显示亮度修复完成
2025-08-27 19:08:35.638 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 19:08:35.640 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1612 | 🔧 [P1-2修复] 3次重试已用尽，可能数据导入尚未完成
2025-08-27 19:08:44.933 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:654 | 数据导入功能被触发，发出 import_requested 信号。
2025-08-27 19:08:44.934 | INFO     | src.gui.prototype.prototype_main_window:_get_suggested_target_path:8374 | 检测到当前在工资表TAB，生成工资表默认路径
2025-08-27 19:08:44.937 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5906 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 08月 > 全部在职人员。打开导入对话框。
2025-08-27 19:08:44.938 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-08-27 19:08:44.940 | INFO     | src.modules.data_import.change_data_config_manager:__init__:43 | 配置管理器初始化完成，配置目录: C:\test\salary_changes\salary_changes\state\change_data_configs
2025-08-27 19:08:44.941 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:78 | 多Sheet导入器初始化完成
2025-08-27 19:08:44.943 | INFO     | src.gui.widgets.target_selection_widget:_load_available_options:250 | 成功加载导航配置
2025-08-27 19:08:44.957 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-27 19:08:45.012 | INFO     | src.gui.main_dialogs:_get_template_fields:2010 | 🔧 [P3-修复] ConfigSyncManager未初始化，使用默认字段模板
2025-08-27 19:08:45.013 | INFO     | src.gui.main_dialogs:_init_field_mapping:1997 | 🔧 [P3-修复] 未找到字段模板，使用内置默认字段列表
2025-08-27 19:08:45.086 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-08-27 19:08:45.087 | INFO     | src.modules.data_import.import_defaults_manager:get_smart_defaults_for_category:244 | 检测到工资表导入，设置table_template为salary_data
2025-08-27 19:08:45.090 | INFO     | src.gui.main_dialogs:_apply_default_settings:2616 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'separate_tables', 'table_template': 'salary_data'}
2025-08-27 19:08:45.091 | INFO     | src.gui.main_dialogs:_setup_tooltips:2989 | 工具提示设置完成
2025-08-27 19:08:45.092 | INFO     | src.gui.main_dialogs:_setup_shortcuts:3028 | 快捷键设置完成
2025-08-27 19:08:45.093 | INFO     | src.gui.main_dialogs:__init__:82 | 数据导入对话框初始化完成。
2025-08-27 19:08:45.094 | INFO     | src.modules.data_import.multi_sheet_importer:set_config_sync_manager:87 | 🔧 [修复] ConfigSyncManager已设置到MultiSheetImporter
2025-08-27 19:08:45.095 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5917 | 🔧 [P0-修复] ConfigSyncManager已设置到数据导入对话框
2025-08-27 19:08:47.118 | INFO     | src.gui.main_dialogs:_on_target_changed:2522 | 目标位置已更新: 异动人员表 > 2025年 > 08月 > 全部在职人员
2025-08-27 19:08:47.119 | INFO     | src.gui.main_dialogs:_on_change_mode_changed:2284 | 🔧 [P2-1修复] 异动表处理模式已切换为: 动态处理（保持原始）
2025-08-27 19:08:47.122 | INFO     | src.gui.main_dialogs:_on_target_changed:2541 | 数据期间自动同步为: 2025-08
2025-08-27 19:08:47.123 | INFO     | src.gui.main_dialogs:_on_target_changed:2561 | 数据描述自动生成: 2025年08月全部在职人员工资
2025-08-27 19:08:49.326 | INFO     | src.gui.main_dialogs:_on_target_changed:2522 | 目标位置已更新: 异动人员表 > 2025年 > 12月 > 全部在职人员
2025-08-27 19:08:49.326 | INFO     | src.gui.main_dialogs:_on_target_changed:2541 | 数据期间自动同步为: 2025-12
2025-08-27 19:08:49.334 | INFO     | src.gui.main_dialogs:_on_target_changed:2561 | 数据描述自动生成: 2025年12月全部在职人员工资
2025-08-27 19:08:57.466 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-27 19:08:58.882 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-08-27 19:08:58.883 | INFO     | src.gui.main_dialogs:_auto_select_sheet_by_category:2651 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-08-27 19:09:15.022 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-27 19:09:15.023 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-27 19:09:15.027 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-27 19:09:15.154 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 23列 (列过滤: 否)
2025-08-27 19:09:15.157 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-27 19:09:15.160 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-08-27 19:09:15.160 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1397行 x 23列
2025-08-27 19:09:15.165 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-27 19:09:15.170 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-08-27 19:09:15.172 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1396行 × 23列
2025-08-27 19:09:15.229 | INFO     | src.modules.data_import.change_data_config_manager:__init__:43 | 配置管理器初始化完成，配置目录: C:\test\salary_changes\salary_changes\state\change_data_configs
2025-08-27 19:09:15.230 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: number
2025-08-27 19:09:15.233 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: string
2025-08-27 19:09:15.234 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: date
2025-08-27 19:09:15.234 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: code
2025-08-27 19:09:15.235 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: custom
2025-08-27 19:09:15.236 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: salary_float - 工资金额
2025-08-27 19:09:15.237 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: employee_id_string - 工号
2025-08-27 19:09:15.237 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: name_string - 姓名
2025-08-27 19:09:15.238 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: date_string - 日期
2025-08-27 19:09:15.239 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: id_number_string - 身份证号
2025-08-27 19:09:15.239 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: code_string - 代码
2025-08-27 19:09:15.240 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: float - 浮点数
2025-08-27 19:09:15.249 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: integer - 整数
2025-08-27 19:09:15.251 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: text_string - 文本字符串
2025-08-27 19:09:15.252 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: personnel_category_code - 人员类别代码
2025-08-27 19:09:15.253 | INFO     | src.modules.data_import.field_type_manager:__init__:41 | 字段类型管理器初始化完成，存储目录: C:\test\salary_changes\salary_changes\state\field_types
2025-08-27 19:09:15.755 | INFO     | src.gui.change_data_config_dialog:load_all_sheets:1462 | 成功加载 4 个工作表
2025-08-27 19:09:50.390 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1626 | 行5字段类型变更为personnel_category_code，示例数据更新为: 01
2025-08-27 19:09:50.414 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 19:10:04.327 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1626 | 行12字段类型变更为salary_float，示例数据更新为: 0.00
2025-08-27 19:10:04.345 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 19:10:07.375 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1626 | 行16字段类型变更为salary_float，示例数据更新为: 1,690.00
2025-08-27 19:10:07.394 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 19:10:12.743 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1626 | 行19字段类型变更为salary_float，示例数据更新为: 352.00
2025-08-27 19:10:12.763 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 19:10:15.703 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1626 | 行22字段类型变更为salary_float，示例数据更新为: 2,097.00
2025-08-27 19:10:15.727 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 19:10:32.602 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 19:10:55.191 | INFO     | src.gui.main_dialogs:_on_change_data_config_saved:2770 | 异动表配置已保存 (工作表: unknown): 23 个字段
2025-08-27 19:10:58.608 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-27 19:10:58.608 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-27 19:10:58.611 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-27 19:10:58.736 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 23列 (列过滤: 否)
2025-08-27 19:10:58.739 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-27 19:10:58.743 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-08-27 19:10:58.743 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1397行 x 23列
2025-08-27 19:10:58.748 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-27 19:10:58.753 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-08-27 19:10:58.755 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1396行 × 23列
2025-08-27 19:10:58.757 | INFO     | src.modules.data_import.change_data_config_manager:__init__:43 | 配置管理器初始化完成，配置目录: C:\test\salary_changes\salary_changes\state\change_data_configs
2025-08-27 19:10:58.758 | INFO     | src.modules.data_import.field_type_manager:__init__:41 | 字段类型管理器初始化完成，存储目录: C:\test\salary_changes\salary_changes\state\field_types
2025-08-27 19:10:59.274 | INFO     | src.gui.change_data_config_dialog:load_all_sheets:1462 | 成功加载 4 个工作表
2025-08-27 19:10:59.505 | INFO     | src.gui.main_dialogs:_open_change_data_config_dialog:2725 | 为工作表 'unknown' 加载专用配置
2025-08-27 19:10:59.506 | INFO     | src.gui.change_data_config_dialog:apply_saved_configuration:1951 | 应用之前保存的配置，配置包含: dict_keys(['field_mapping', 'field_types', 'formatting_rules'])
2025-08-27 19:10:59.508 | INFO     | src.gui.change_data_config_dialog:_apply_config_to_table:1896 | 应用字段类型配置，共23个字段
2025-08-27 19:10:59.512 | INFO     | src.gui.change_data_config_dialog:_apply_config_to_table:1923 | 设置字段 序号 类型为 integer: 成功
2025-08-27 19:10:59.512 | INFO     | src.gui.change_data_config_dialog:_apply_config_to_table:1923 | 设置字段 工号 类型为 employee_id_string: 成功
2025-08-27 19:10:59.513 | INFO     | src.gui.change_data_config_dialog:_apply_config_to_table:1923 | 设置字段 姓名 类型为 name_string: 成功
2025-08-27 19:10:59.514 | INFO     | src.gui.change_data_config_dialog:_apply_config_to_table:1923 | 设置字段 部门名称 类型为 text_string: 成功
2025-08-27 19:10:59.515 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1626 | 行5字段类型变更为personnel_category_code，示例数据更新为: 01
2025-08-27 19:10:59.541 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 19:10:59.542 | INFO     | src.gui.change_data_config_dialog:_apply_config_to_table:1923 | 设置字段 人员类别代码 类型为 personnel_category_code: 成功
2025-08-27 19:10:59.545 | INFO     | src.gui.change_data_config_dialog:_apply_config_to_table:1923 | 设置字段 人员类别 类型为 text_string: 成功
2025-08-27 19:10:59.546 | INFO     | src.gui.change_data_config_dialog:_apply_config_to_table:1923 | 设置字段 2025年岗位工资 类型为 salary_float: 成功
2025-08-27 19:10:59.547 | INFO     | src.gui.change_data_config_dialog:_apply_config_to_table:1923 | 设置字段 2025年薪级工资 类型为 salary_float: 成功
2025-08-27 19:10:59.548 | INFO     | src.gui.change_data_config_dialog:_apply_config_to_table:1923 | 设置字段 津贴 类型为 salary_float: 成功
2025-08-27 19:10:59.549 | INFO     | src.gui.change_data_config_dialog:_apply_config_to_table:1923 | 设置字段 结余津贴 类型为 salary_float: 成功
2025-08-27 19:10:59.550 | INFO     | src.gui.change_data_config_dialog:_apply_config_to_table:1923 | 设置字段 2025年基础性绩效 类型为 salary_float: 成功
2025-08-27 19:10:59.551 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1626 | 行12字段类型变更为salary_float，示例数据更新为: 0.00
2025-08-27 19:10:59.573 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 19:10:59.573 | INFO     | src.gui.change_data_config_dialog:_apply_config_to_table:1923 | 设置字段 卫生费 类型为 salary_float: 成功
2025-08-27 19:10:59.577 | INFO     | src.gui.change_data_config_dialog:_apply_config_to_table:1923 | 设置字段 交通补贴 类型为 salary_float: 成功
2025-08-27 19:10:59.577 | INFO     | src.gui.change_data_config_dialog:_apply_config_to_table:1923 | 设置字段 物业补贴 类型为 salary_float: 成功
2025-08-27 19:10:59.578 | INFO     | src.gui.change_data_config_dialog:_apply_config_to_table:1923 | 设置字段 住房补贴 类型为 salary_float: 成功
2025-08-27 19:10:59.579 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1626 | 行16字段类型变更为salary_float，示例数据更新为: 1,690.00
2025-08-27 19:10:59.602 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 19:10:59.602 | INFO     | src.gui.change_data_config_dialog:_apply_config_to_table:1923 | 设置字段 车补 类型为 salary_float: 成功
2025-08-27 19:10:59.605 | INFO     | src.gui.change_data_config_dialog:_apply_config_to_table:1923 | 设置字段 通讯补贴 类型为 salary_float: 成功
2025-08-27 19:10:59.606 | INFO     | src.gui.change_data_config_dialog:_apply_config_to_table:1923 | 设置字段 2025年奖励性绩效预发 类型为 salary_float: 成功
2025-08-27 19:10:59.607 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1626 | 行19字段类型变更为salary_float，示例数据更新为: 352.00
2025-08-27 19:10:59.630 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 19:10:59.631 | INFO     | src.gui.change_data_config_dialog:_apply_config_to_table:1923 | 设置字段 补发 类型为 salary_float: 成功
2025-08-27 19:10:59.633 | INFO     | src.gui.change_data_config_dialog:_apply_config_to_table:1923 | 设置字段 借支 类型为 salary_float: 成功
2025-08-27 19:10:59.634 | INFO     | src.gui.change_data_config_dialog:_apply_config_to_table:1923 | 设置字段 应发工资 类型为 salary_float: 成功
2025-08-27 19:10:59.635 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1626 | 行22字段类型变更为salary_float，示例数据更新为: 2,097.00
2025-08-27 19:10:59.660 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 19:10:59.661 | INFO     | src.gui.change_data_config_dialog:_apply_config_to_table:1923 | 设置字段 2025公积金 类型为 salary_float: 成功
2025-08-27 19:10:59.664 | INFO     | src.gui.change_data_config_dialog:_apply_config_to_table:1923 | 设置字段 代扣代存养老保险 类型为 salary_float: 成功
2025-08-27 19:10:59.693 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 19:10:59.697 | INFO     | src.gui.change_data_config_dialog:_apply_config_to_table:1940 | 配置应用到表格成功
2025-08-27 19:11:18.856 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 19:11:19.979 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 19:11:21.298 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 19:12:22.995 | INFO     | __main__:main:519 | 应用程序正常退出
2025-08-27 19:38:46.471 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-08-27 19:38:46.472 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-08-27 19:38:46.472 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-08-27 19:38:46.473 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-08-27 19:38:46.473 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-08-27 19:38:46.474 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-27 19:38:49.843 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-08-27 19:38:49.843 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-08-27 19:38:49.844 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-08-27 19:38:49.845 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-08-27 19:38:49.845 | INFO     | __main__:setup_app_logging:423 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-08-27 19:38:49.846 | INFO     | __main__:main:487 | 初始化核心管理器...
2025-08-27 19:38:49.846 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-27 19:38:49.847 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-27 19:38:49.847 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-27 19:38:49.847 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-27 19:38:49.847 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-27 19:38:49.862 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-27 19:38:49.862 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-08-27 19:38:49.862 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-27 19:38:49.871 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-27 19:38:49.873 | INFO     | __main__:main:492 | 核心管理器初始化完成。
2025-08-27 19:38:49.876 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-08-27 19:38:49.876 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-08-27 19:38:49.877 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-27 19:38:49.878 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-08-27 19:38:49.878 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-08-27 19:38:49.878 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-08-27 19:38:49.878 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-08-27 19:38:49.885 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:11817 | 🔧 [P2-3] 错误恢复策略注册完成
2025-08-27 19:38:49.891 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-27 19:38:49.898 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:11672 | 🔧 [P2-3] 错误处理机制设置完成
2025-08-27 19:38:49.899 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:11710 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-08-27 19:38:50.114 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-08-27 19:38:50.114 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-08-27 19:38:50.117 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-27 19:38:50.119 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-08-27 19:38:50.120 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-27 19:38:50.121 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-27 19:38:50.121 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-27 19:38:50.122 | INFO     | src.core.field_mapping_manager:_load_config:89 | 🔧 [P3优化] 字段映射配置加载成功
2025-08-27 19:38:50.122 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-08-27 19:38:50.122 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-27 19:38:50.128 | INFO     | src.modules.format_management.field_registry:__init__:95 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-08-27 19:38:50.133 | INFO     | src.core.unified_mapping_service:__init__:45 | UnifiedMappingService 初始化完成
2025-08-27 19:38:50.133 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-08-27 19:38:50.134 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-08-27 19:38:50.136 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-27 19:38:50.137 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-27 19:38:50.138 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-08-27 19:38:50.138 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 21.8ms
2025-08-27 19:38:50.163 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-08-27 19:38:50.164 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-08-27 19:38:50.167 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-08-27 19:38:50.167 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-08-27 19:38:50.172 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-08-27 19:38:50.173 | INFO     | src.gui.prototype.prototype_main_window:__init__:3650 | 🚀 性能管理器已集成
2025-08-27 19:38:50.174 | INFO     | src.gui.prototype.prototype_main_window:__init__:3652 | ✅ 新架构集成成功！
2025-08-27 19:38:50.175 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3765 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-08-27 19:38:50.175 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3730 | ✅ 新架构事件监听器设置完成
2025-08-27 19:38:50.176 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-08-27 19:38:50.177 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-08-27 19:38:50.183 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-08-27 19:38:50.474 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2726 | 菜单栏创建完成
2025-08-27 19:38:50.474 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-27 19:38:50.480 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: user_preferences.json
2025-08-27 19:38:50.481 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-08-27 19:38:50.484 | INFO     | src.gui.prototype.prototype_main_window:__init__:2701 | 菜单栏管理器初始化完成
2025-08-27 19:38:50.486 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-08-27 19:38:50.487 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5458 | 管理器设置完成，包含增强版表头管理器
2025-08-27 19:38:50.487 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5463 | 🔧 开始应用窗口级Material Design样式...
2025-08-27 19:38:50.491 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-08-27 19:38:50.491 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-08-27 19:38:50.497 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5470 | ✅ 窗口级样式应用成功
2025-08-27 19:38:50.497 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5511 | ✅ 响应式样式监听设置完成
2025-08-27 19:38:50.502 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-27 19:38:50.504 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-27 19:38:50.504 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-27 19:38:50.514 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-27 19:38:50.517 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-27 19:38:50.534 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-27 19:38:50.538 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1835 | 🏠 使用示例数据结构加载导航（首次启动或暂无数据时显示）
2025-08-27 19:38:50.538 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:2037 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-27 19:38:50.538 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-27 19:38:50.538 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:924 | 恢复导航状态: 0个展开项
2025-08-27 19:38:50.538 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月', '工资表 > 2025年 > 05月 > 全部在职人员']
2025-08-27 19:38:50.566 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-27 19:38:50.569 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-27 19:38:50.580 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-27 19:38:50.590 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-27 19:38:50.593 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:2037 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-27 19:38:50.596 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-27 19:38:50.597 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-27 19:38:50.603 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-27 19:38:50.605 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 19:38:50.611 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1601 | 🔧 [P1-2修复] 检查表状态失败，1.0s后重试...
2025-08-27 19:38:50.612 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1405 | 🔧 [P1-2修复] 未找到最新工资数据路径（可能是首次启动）
2025-08-27 19:38:50.890 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-08-27 19:38:50.891 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2142 | 🚨 [架构修复] 启用模型数据同步机制
2025-08-27 19:38:50.896 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1366 | 快捷键注册完成: 18/18 个
2025-08-27 19:38:50.897 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1809 | 拖拽排序管理器初始化完成
2025-08-27 19:38:50.922 | INFO     | src.modules.data_management.data_flow_validator:__init__:85 | 🔧 [数据验证器] 初始化完成，验证级别: lenient
2025-08-27 19:38:50.923 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-08-27 19:38:50.927 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-08-27 19:38:50.928 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2195 | 🔧 [P0-紧急修复] 数据流验证器初始化成功（宽松模式）
2025-08-27 19:38:50.929 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-08-27 19:38:50.930 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-27 19:38:50.932 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-27 19:38:50.933 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2247 | 🎯 [统一格式管理] 使用统一格式管理器
2025-08-27 19:38:50.952 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:370 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-27 19:38:50.953 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-08-27 19:38:50.956 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2294 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-08-27 19:38:50.957 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1553 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-08-27 19:38:50.958 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1554 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-08-27 19:38:50.959 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1555 | 🔧 [列宽保存修复] 配置文件存在: True
2025-08-27 19:38:50.959 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1556 | 🔧 [列宽保存修复] 父目录存在: True
2025-08-27 19:38:50.961 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1557 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-08-27 19:38:50.973 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2301 | 列宽管理器初始化完成
2025-08-27 19:38:50.974 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2428 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-08-27 19:38:50.977 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2315 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-08-27 19:38:50.978 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-27 19:38:50.979 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-27 19:38:50.980 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-27 19:38:50.996 | INFO     | src.gui.prototype.widgets.pagination_state_manager:__init__:78 | 🔧 [P1优化] 分页状态管理器初始化完成
2025-08-27 19:38:50.998 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 0
2025-08-27 19:38:51.014 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2838 | 🔧 [P1优化] 表切换操作: None -> default_table
2025-08-27 19:38:51.015 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-27 19:38:51.028 | INFO     | src.gui.prototype.widgets.table_header_cache:__init__:67 | 🔧 [P1优化] 表头缓存管理器初始化完成，最大缓存: 100
2025-08-27 19:38:51.050 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2890 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-27 19:38:51.051 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-27 19:38:51.067 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-27 19:38:51.073 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-27 19:38:51.074 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-27 19:38:51.076 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-27 19:38:51.076 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-27 19:38:51.079 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-08-27 19:38:51.081 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 94.9ms
2025-08-27 19:38:51.087 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-27 19:38:51.090 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-27 19:38:51.096 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-27 19:38:51.097 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-27 19:38:51.098 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-27 19:38:51.112 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-08-27 19:38:51.126 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-08-27 19:38:51.127 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-08-27 19:38:51.185 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:594 | 控制面板按钮信号连接完成
2025-08-27 19:38:51.241 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-27 19:38:51.242 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-27 19:38:51.245 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5420 | 快捷键设置完成
2025-08-27 19:38:51.246 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5377 | 主窗口UI设置完成。
2025-08-27 19:38:51.247 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5614 | 🔧 [全局排序] 全局排序开关连接成功
2025-08-27 19:38:51.247 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5646 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-08-27 19:38:51.249 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5658 | ✅ 已连接分页刷新信号到主窗口
2025-08-27 19:38:51.249 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5659 | ✅ 已连接分页组件事件到新架构
2025-08-27 19:38:51.251 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5670 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-08-27 19:38:51.252 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5673 | 信号连接设置完成
2025-08-27 19:38:51.253 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6849 | 🔧 [P1-2修复] 发现 2 个表的配置
2025-08-27 19:38:51.260 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: salary_data_2025_07_active_employees, 10个字段
2025-08-27 19:38:51.261 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: active_employees, 5个字段
2025-08-27 19:38:51.262 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6859 | ✅ [P1-2修复] 已加载字段映射信息，共2个表的映射
2025-08-27 19:38:51.276 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-27 19:38:51.277 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-27 19:38:51.277 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-27 19:38:51.278 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-27 19:38:51.279 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-27 19:38:51.279 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 22
2025-08-27 19:38:51.281 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-27 19:38:51.281 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-27 19:38:51.284 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-27 19:38:51.291 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-27 19:38:51.293 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 14.8ms
2025-08-27 19:38:51.293 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-27 19:38:51.294 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-27 19:38:51.296 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-27 19:38:51.298 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-27 19:38:51.302 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-27 19:38:51.306 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-27 19:38:51.307 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8551 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-08-27 19:38:51.308 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-27 19:38:51.309 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-27 19:38:51.309 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-27 19:38:51.310 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-27 19:38:51.311 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-27 19:38:51.312 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-27 19:38:51.312 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-27 19:38:51.313 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-27 19:38:51.314 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-27 19:38:51.322 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 12.5ms
2025-08-27 19:38:51.322 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-27 19:38:51.323 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-27 19:38:51.324 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-27 19:38:51.325 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-27 19:38:51.326 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-27 19:38:51.332 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8569 | 已显示标准空表格，表头数量: 22
2025-08-27 19:38:51.338 | INFO     | src.gui.prototype.prototype_main_window:__init__:3704 | 原型主窗口初始化完成
2025-08-27 19:38:51.417 | INFO     | __main__:main:514 | 应用程序启动成功
2025-08-27 19:38:51.489 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-27 19:38:51.492 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1474 | 执行延迟的自动选择最新数据...
2025-08-27 19:38:51.493 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 19:38:51.495 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1497 | 延迟自动选择最新数据失败，可能没有可用数据
2025-08-27 19:38:51.500 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-27 19:38:51.501 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-27 19:38:51.575 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-08-27 19:38:51.577 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2079 | MainWorkspaceArea 响应式适配: sm
2025-08-27 19:38:51.635 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 19:38:52.086 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9453 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet']
2025-08-27 19:38:52.086 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9363 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-27 19:38:52.090 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9377 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet']
2025-08-27 19:38:52.091 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:9911 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet']
2025-08-27 19:38:52.108 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9383 | 🔧 [P0-1] 智能显示亮度修复完成
2025-08-27 19:38:52.496 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 19:38:52.497 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1612 | 🔧 [P1-2修复] 3次重试已用尽，可能数据导入尚未完成
2025-08-27 19:40:16.127 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:654 | 数据导入功能被触发，发出 import_requested 信号。
2025-08-27 19:40:16.127 | INFO     | src.gui.prototype.prototype_main_window:_get_suggested_target_path:8374 | 检测到当前在工资表TAB，生成工资表默认路径
2025-08-27 19:40:16.131 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5906 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 08月 > 全部在职人员。打开导入对话框。
2025-08-27 19:40:16.133 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-08-27 19:40:16.134 | INFO     | src.modules.data_import.change_data_config_manager:__init__:43 | 配置管理器初始化完成，配置目录: C:\test\salary_changes\salary_changes\state\change_data_configs
2025-08-27 19:40:16.135 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:78 | 多Sheet导入器初始化完成
2025-08-27 19:40:16.136 | INFO     | src.gui.widgets.target_selection_widget:_load_available_options:250 | 成功加载导航配置
2025-08-27 19:40:16.151 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-27 19:40:16.211 | INFO     | src.gui.main_dialogs:_get_template_fields:2010 | 🔧 [P3-修复] ConfigSyncManager未初始化，使用默认字段模板
2025-08-27 19:40:16.213 | INFO     | src.gui.main_dialogs:_init_field_mapping:1997 | 🔧 [P3-修复] 未找到字段模板，使用内置默认字段列表
2025-08-27 19:40:16.292 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-08-27 19:40:16.292 | INFO     | src.modules.data_import.import_defaults_manager:get_smart_defaults_for_category:244 | 检测到工资表导入，设置table_template为salary_data
2025-08-27 19:40:16.300 | INFO     | src.gui.main_dialogs:_apply_default_settings:2616 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'separate_tables', 'table_template': 'salary_data'}
2025-08-27 19:40:16.300 | INFO     | src.gui.main_dialogs:_setup_tooltips:2989 | 工具提示设置完成
2025-08-27 19:40:16.301 | INFO     | src.gui.main_dialogs:_setup_shortcuts:3028 | 快捷键设置完成
2025-08-27 19:40:16.302 | INFO     | src.gui.main_dialogs:__init__:82 | 数据导入对话框初始化完成。
2025-08-27 19:40:16.302 | INFO     | src.modules.data_import.multi_sheet_importer:set_config_sync_manager:87 | 🔧 [修复] ConfigSyncManager已设置到MultiSheetImporter
2025-08-27 19:40:16.302 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5917 | 🔧 [P0-修复] ConfigSyncManager已设置到数据导入对话框
2025-08-27 19:40:18.473 | INFO     | src.gui.main_dialogs:_on_target_changed:2522 | 目标位置已更新: 异动人员表 > 2025年 > 08月 > 全部在职人员
2025-08-27 19:40:18.474 | INFO     | src.gui.main_dialogs:_on_change_mode_changed:2284 | 🔧 [P2-1修复] 异动表处理模式已切换为: 动态处理（保持原始）
2025-08-27 19:40:18.476 | INFO     | src.gui.main_dialogs:_on_target_changed:2541 | 数据期间自动同步为: 2025-08
2025-08-27 19:40:18.477 | INFO     | src.gui.main_dialogs:_on_target_changed:2561 | 数据描述自动生成: 2025年08月全部在职人员工资
2025-08-27 19:40:21.184 | INFO     | src.gui.main_dialogs:_on_target_changed:2522 | 目标位置已更新: 异动人员表 > 2025年 > 12月 > 全部在职人员
2025-08-27 19:40:21.185 | INFO     | src.gui.main_dialogs:_on_target_changed:2541 | 数据期间自动同步为: 2025-12
2025-08-27 19:40:21.188 | INFO     | src.gui.main_dialogs:_on_target_changed:2561 | 数据描述自动生成: 2025年12月全部在职人员工资
2025-08-27 19:40:28.018 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-27 19:40:29.196 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-08-27 19:40:29.197 | INFO     | src.gui.main_dialogs:_auto_select_sheet_by_category:2651 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-08-27 19:40:36.393 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-27 19:40:36.394 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-27 19:40:36.395 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-27 19:40:36.543 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 23列 (列过滤: 否)
2025-08-27 19:40:36.546 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-27 19:40:36.551 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-08-27 19:40:36.552 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1397行 x 23列
2025-08-27 19:40:36.560 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-27 19:40:36.568 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-08-27 19:40:36.570 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1396行 × 23列
2025-08-27 19:40:36.629 | INFO     | src.modules.data_import.change_data_config_manager:__init__:43 | 配置管理器初始化完成，配置目录: C:\test\salary_changes\salary_changes\state\change_data_configs
2025-08-27 19:40:36.630 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: number
2025-08-27 19:40:36.633 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: string
2025-08-27 19:40:36.633 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: date
2025-08-27 19:40:36.634 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: code
2025-08-27 19:40:36.635 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: custom
2025-08-27 19:40:36.636 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: salary_float - 工资金额
2025-08-27 19:40:36.637 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: employee_id_string - 工号
2025-08-27 19:40:36.638 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: name_string - 姓名
2025-08-27 19:40:36.638 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: date_string - 日期
2025-08-27 19:40:36.638 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: id_number_string - 身份证号
2025-08-27 19:40:36.638 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: code_string - 代码
2025-08-27 19:40:36.638 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: float - 浮点数
2025-08-27 19:40:36.638 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: integer - 整数
2025-08-27 19:40:36.638 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: text_string - 文本字符串
2025-08-27 19:40:36.644 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: personnel_category_code - 人员类别代码
2025-08-27 19:40:36.653 | INFO     | src.modules.data_import.field_type_manager:__init__:41 | 字段类型管理器初始化完成，存储目录: C:\test\salary_changes\salary_changes\state\field_types
2025-08-27 19:40:37.208 | INFO     | src.gui.change_data_config_dialog:load_all_sheets:1462 | 成功加载 4 个工作表
2025-08-27 19:40:48.305 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1626 | 行5字段类型变更为personnel_category_code，示例数据更新为: 01
2025-08-27 19:40:48.332 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 19:41:02.041 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1626 | 行5字段类型变更为code_string，示例数据更新为: 1.0
2025-08-27 19:41:02.061 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 19:41:08.809 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1626 | 行5字段类型变更为personnel_category_code，示例数据更新为: 01
2025-08-27 19:41:08.828 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 19:41:16.808 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1626 | 行12字段类型变更为salary_float，示例数据更新为: 0.00
2025-08-27 19:41:16.833 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 19:41:27.497 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1626 | 行19字段类型变更为salary_float，示例数据更新为: 352.00
2025-08-27 19:41:27.518 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 19:41:31.129 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1626 | 行22字段类型变更为salary_float，示例数据更新为: 2,097.00
2025-08-27 19:41:31.150 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 19:41:36.145 | INFO     | src.gui.change_data_config_dialog:_on_field_type_changed:1626 | 行16字段类型变更为salary_float，示例数据更新为: 1,690.00
2025-08-27 19:41:36.164 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 19:41:56.674 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 19:42:36.511 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 19:42:59.461 | INFO     | src.gui.change_data_config_dialog:refresh_preview:715 | 预览已刷新
2025-08-27 19:43:51.568 | INFO     | src.modules.data_import.change_data_config_manager:import_config:492 | 配置从 C:/test/salary_changes/salary_changes/state/change_data_configs/user_configs/tt1.json 导入为 'tt1'
2025-08-27 19:44:05.023 | INFO     | src.gui.change_data_config_dialog:on_sheet_changed:1483 | 切换到工作表: 退休人员工资表
2025-08-27 19:44:20.674 | INFO     | src.gui.change_data_config_dialog:on_sheet_changed:1474 | 已保存工作表 '退休人员工资表' 的配置
2025-08-27 19:44:20.879 | INFO     | src.gui.change_data_config_dialog:on_sheet_changed:1483 | 切换到工作表: 全部在职人员工资表
2025-08-27 19:44:30.065 | INFO     | src.gui.change_data_config_dialog:on_sheet_changed:1474 | 已保存工作表 '全部在职人员工资表' 的配置
2025-08-27 19:44:30.238 | INFO     | src.gui.change_data_config_dialog:on_sheet_changed:1483 | 切换到工作表: A岗职工
2025-08-27 19:45:35.499 | INFO     | __main__:main:519 | 应用程序正常退出
2025-08-27 20:09:15.437 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-08-27 20:09:15.437 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-08-27 20:09:15.438 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-08-27 20:09:15.438 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-08-27 20:09:15.439 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-08-27 20:09:15.441 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-27 20:09:18.599 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-08-27 20:09:18.600 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-08-27 20:09:18.600 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-08-27 20:09:18.601 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-08-27 20:09:18.601 | INFO     | __main__:setup_app_logging:423 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-08-27 20:09:18.602 | INFO     | __main__:main:487 | 初始化核心管理器...
2025-08-27 20:09:18.603 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-27 20:09:18.604 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-27 20:09:18.605 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-27 20:09:18.605 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-27 20:09:18.607 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-27 20:09:18.616 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-27 20:09:18.617 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-08-27 20:09:18.618 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-27 20:09:18.625 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-27 20:09:18.627 | INFO     | __main__:main:492 | 核心管理器初始化完成。
2025-08-27 20:09:18.630 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-08-27 20:09:18.631 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-08-27 20:09:18.632 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-27 20:09:18.632 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-08-27 20:09:18.633 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-08-27 20:09:18.634 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-08-27 20:09:18.635 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-08-27 20:09:18.640 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:11817 | 🔧 [P2-3] 错误恢复策略注册完成
2025-08-27 20:09:18.642 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-27 20:09:18.643 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:11672 | 🔧 [P2-3] 错误处理机制设置完成
2025-08-27 20:09:18.644 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:11710 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-08-27 20:09:18.844 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-08-27 20:09:18.847 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-08-27 20:09:18.849 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-27 20:09:18.851 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-08-27 20:09:18.852 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-27 20:09:18.853 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-27 20:09:18.853 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-27 20:09:18.854 | INFO     | src.core.field_mapping_manager:_load_config:89 | 🔧 [P3优化] 字段映射配置加载成功
2025-08-27 20:09:18.857 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-08-27 20:09:18.866 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-27 20:09:18.868 | INFO     | src.modules.format_management.field_registry:__init__:95 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-08-27 20:09:18.869 | INFO     | src.core.unified_mapping_service:__init__:45 | UnifiedMappingService 初始化完成
2025-08-27 20:09:18.869 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-08-27 20:09:18.870 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-08-27 20:09:18.872 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-27 20:09:18.873 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-27 20:09:18.874 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-08-27 20:09:18.880 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 30.3ms
2025-08-27 20:09:18.906 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-08-27 20:09:18.907 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-08-27 20:09:18.911 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-08-27 20:09:18.911 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-08-27 20:09:18.912 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-08-27 20:09:18.913 | INFO     | src.gui.prototype.prototype_main_window:__init__:3650 | 🚀 性能管理器已集成
2025-08-27 20:09:18.914 | INFO     | src.gui.prototype.prototype_main_window:__init__:3652 | ✅ 新架构集成成功！
2025-08-27 20:09:18.915 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3765 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-08-27 20:09:18.916 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3730 | ✅ 新架构事件监听器设置完成
2025-08-27 20:09:18.916 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-08-27 20:09:18.918 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-08-27 20:09:18.918 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-08-27 20:09:19.204 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2726 | 菜单栏创建完成
2025-08-27 20:09:19.205 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-27 20:09:19.208 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: user_preferences.json
2025-08-27 20:09:19.209 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-08-27 20:09:19.213 | INFO     | src.gui.prototype.prototype_main_window:__init__:2701 | 菜单栏管理器初始化完成
2025-08-27 20:09:19.213 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-08-27 20:09:19.213 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5458 | 管理器设置完成，包含增强版表头管理器
2025-08-27 20:09:19.214 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5463 | 🔧 开始应用窗口级Material Design样式...
2025-08-27 20:09:19.216 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-08-27 20:09:19.222 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-08-27 20:09:19.223 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5470 | ✅ 窗口级样式应用成功
2025-08-27 20:09:19.224 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5511 | ✅ 响应式样式监听设置完成
2025-08-27 20:09:19.232 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-27 20:09:19.236 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-27 20:09:19.236 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-27 20:09:19.239 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-27 20:09:19.242 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-27 20:09:19.251 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-27 20:09:19.259 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1835 | 🏠 使用示例数据结构加载导航（首次启动或暂无数据时显示）
2025-08-27 20:09:19.263 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:2037 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-27 20:09:19.264 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-27 20:09:19.265 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:924 | 恢复导航状态: 0个展开项
2025-08-27 20:09:19.265 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月', '工资表 > 2025年 > 05月 > 全部在职人员']
2025-08-27 20:09:19.266 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-27 20:09:19.266 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-27 20:09:19.269 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-27 20:09:19.274 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-27 20:09:19.279 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:2037 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-27 20:09:19.280 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-27 20:09:19.281 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-27 20:09:19.282 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-27 20:09:19.283 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 20:09:19.284 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1601 | 🔧 [P1-2修复] 检查表状态失败，1.0s后重试...
2025-08-27 20:09:19.284 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1405 | 🔧 [P1-2修复] 未找到最新工资数据路径（可能是首次启动）
2025-08-27 20:09:19.565 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-08-27 20:09:19.567 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2142 | 🚨 [架构修复] 启用模型数据同步机制
2025-08-27 20:09:19.572 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1366 | 快捷键注册完成: 18/18 个
2025-08-27 20:09:19.573 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1809 | 拖拽排序管理器初始化完成
2025-08-27 20:09:19.595 | INFO     | src.modules.data_management.data_flow_validator:__init__:85 | 🔧 [数据验证器] 初始化完成，验证级别: lenient
2025-08-27 20:09:19.595 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-08-27 20:09:19.598 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-08-27 20:09:19.600 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2195 | 🔧 [P0-紧急修复] 数据流验证器初始化成功（宽松模式）
2025-08-27 20:09:19.601 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-08-27 20:09:19.602 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-27 20:09:19.602 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-27 20:09:19.603 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2247 | 🎯 [统一格式管理] 使用统一格式管理器
2025-08-27 20:09:19.619 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:370 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-27 20:09:19.619 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-08-27 20:09:19.621 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2294 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-08-27 20:09:19.622 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1553 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-08-27 20:09:19.623 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1554 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-08-27 20:09:19.624 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1555 | 🔧 [列宽保存修复] 配置文件存在: True
2025-08-27 20:09:19.625 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1556 | 🔧 [列宽保存修复] 父目录存在: True
2025-08-27 20:09:19.626 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1557 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-08-27 20:09:19.627 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2301 | 列宽管理器初始化完成
2025-08-27 20:09:19.636 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2428 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-08-27 20:09:19.639 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2315 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-08-27 20:09:19.640 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-27 20:09:19.641 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-27 20:09:19.642 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-27 20:09:19.653 | INFO     | src.gui.prototype.widgets.pagination_state_manager:__init__:78 | 🔧 [P1优化] 分页状态管理器初始化完成
2025-08-27 20:09:19.654 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 0
2025-08-27 20:09:19.655 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2838 | 🔧 [P1优化] 表切换操作: None -> default_table
2025-08-27 20:09:19.656 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-27 20:09:19.665 | INFO     | src.gui.prototype.widgets.table_header_cache:__init__:67 | 🔧 [P1优化] 表头缓存管理器初始化完成，最大缓存: 100
2025-08-27 20:09:19.666 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2890 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-27 20:09:19.669 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-27 20:09:19.695 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-27 20:09:19.696 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-27 20:09:19.697 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-27 20:09:19.699 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-27 20:09:19.711 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-27 20:09:19.720 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-08-27 20:09:19.722 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 75.8ms
2025-08-27 20:09:19.723 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-27 20:09:19.725 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-27 20:09:19.732 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-27 20:09:19.734 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-27 20:09:19.735 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-27 20:09:19.748 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-08-27 20:09:19.763 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-08-27 20:09:19.764 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-08-27 20:09:19.816 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:594 | 控制面板按钮信号连接完成
2025-08-27 20:09:19.876 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-27 20:09:19.877 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-27 20:09:19.879 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5420 | 快捷键设置完成
2025-08-27 20:09:19.880 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5377 | 主窗口UI设置完成。
2025-08-27 20:09:19.881 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5614 | 🔧 [全局排序] 全局排序开关连接成功
2025-08-27 20:09:19.882 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5646 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-08-27 20:09:19.884 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5658 | ✅ 已连接分页刷新信号到主窗口
2025-08-27 20:09:19.884 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5659 | ✅ 已连接分页组件事件到新架构
2025-08-27 20:09:19.886 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5670 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-08-27 20:09:19.886 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5673 | 信号连接设置完成
2025-08-27 20:09:19.887 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6849 | 🔧 [P1-2修复] 发现 2 个表的配置
2025-08-27 20:09:19.894 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: salary_data_2025_07_active_employees, 10个字段
2025-08-27 20:09:19.895 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: active_employees, 5个字段
2025-08-27 20:09:19.896 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6859 | ✅ [P1-2修复] 已加载字段映射信息，共2个表的映射
2025-08-27 20:09:19.905 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-27 20:09:19.906 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-27 20:09:19.907 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-27 20:09:19.908 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-27 20:09:19.909 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-27 20:09:19.910 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 22
2025-08-27 20:09:19.914 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-27 20:09:19.914 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-27 20:09:19.915 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-27 20:09:19.920 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-27 20:09:19.921 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 13.5ms
2025-08-27 20:09:19.922 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-27 20:09:19.924 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-27 20:09:19.925 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-27 20:09:19.926 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-27 20:09:19.927 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-27 20:09:19.928 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-27 20:09:19.934 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8551 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-08-27 20:09:19.935 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-27 20:09:19.936 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-27 20:09:19.936 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-27 20:09:19.937 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-27 20:09:19.938 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-27 20:09:19.941 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-27 20:09:19.959 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-27 20:09:19.965 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-27 20:09:19.965 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-27 20:09:19.966 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 29.3ms
2025-08-27 20:09:19.967 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-27 20:09:19.968 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-27 20:09:19.970 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-27 20:09:19.972 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-27 20:09:19.973 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-27 20:09:19.980 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8569 | 已显示标准空表格，表头数量: 22
2025-08-27 20:09:19.980 | INFO     | src.gui.prototype.prototype_main_window:__init__:3704 | 原型主窗口初始化完成
2025-08-27 20:09:20.144 | INFO     | __main__:main:514 | 应用程序启动成功
2025-08-27 20:09:20.153 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-27 20:09:20.154 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-27 20:09:20.155 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1474 | 执行延迟的自动选择最新数据...
2025-08-27 20:09:20.156 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-27 20:09:20.157 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 20:09:20.158 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1497 | 延迟自动选择最新数据失败，可能没有可用数据
2025-08-27 20:09:20.262 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-08-27 20:09:20.269 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2079 | MainWorkspaceArea 响应式适配: sm
2025-08-27 20:09:20.355 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 20:09:20.808 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9453 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet']
2025-08-27 20:09:20.809 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9363 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-27 20:09:20.813 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9377 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet']
2025-08-27 20:09:20.814 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:9911 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet']
2025-08-27 20:09:20.831 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9383 | 🔧 [P0-1] 智能显示亮度修复完成
2025-08-27 20:09:21.159 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 20:09:21.160 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1612 | 🔧 [P1-2修复] 3次重试已用尽，可能数据导入尚未完成
2025-08-27 20:24:11.012 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:654 | 数据导入功能被触发，发出 import_requested 信号。
2025-08-27 20:24:11.014 | INFO     | src.gui.prototype.prototype_main_window:_get_suggested_target_path:8374 | 检测到当前在工资表TAB，生成工资表默认路径
2025-08-27 20:24:11.017 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5906 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 08月 > 全部在职人员。打开导入对话框。
2025-08-27 20:24:11.018 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-08-27 20:24:11.020 | INFO     | src.modules.data_import.change_data_config_manager:__init__:43 | 配置管理器初始化完成，配置目录: C:\test\salary_changes\salary_changes\state\change_data_configs
2025-08-27 20:24:11.022 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:78 | 多Sheet导入器初始化完成
2025-08-27 20:24:11.023 | INFO     | src.gui.widgets.target_selection_widget:_load_available_options:250 | 成功加载导航配置
2025-08-27 20:24:11.036 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-27 20:24:11.088 | INFO     | src.gui.main_dialogs:_get_template_fields:2010 | 🔧 [P3-修复] ConfigSyncManager未初始化，使用默认字段模板
2025-08-27 20:24:11.088 | INFO     | src.gui.main_dialogs:_init_field_mapping:1997 | 🔧 [P3-修复] 未找到字段模板，使用内置默认字段列表
2025-08-27 20:24:11.154 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-08-27 20:24:11.155 | INFO     | src.modules.data_import.import_defaults_manager:get_smart_defaults_for_category:244 | 检测到工资表导入，设置table_template为salary_data
2025-08-27 20:24:11.158 | INFO     | src.gui.main_dialogs:_apply_default_settings:2616 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'separate_tables', 'table_template': 'salary_data'}
2025-08-27 20:24:11.159 | INFO     | src.gui.main_dialogs:_setup_tooltips:2989 | 工具提示设置完成
2025-08-27 20:24:11.161 | INFO     | src.gui.main_dialogs:_setup_shortcuts:3028 | 快捷键设置完成
2025-08-27 20:24:11.161 | INFO     | src.gui.main_dialogs:__init__:82 | 数据导入对话框初始化完成。
2025-08-27 20:24:11.162 | INFO     | src.modules.data_import.multi_sheet_importer:set_config_sync_manager:87 | 🔧 [修复] ConfigSyncManager已设置到MultiSheetImporter
2025-08-27 20:24:11.163 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5917 | 🔧 [P0-修复] ConfigSyncManager已设置到数据导入对话框
2025-08-27 20:24:14.133 | INFO     | src.gui.main_dialogs:_on_target_changed:2522 | 目标位置已更新: 工资表 > 2025年 > 12月 > 全部在职人员
2025-08-27 20:24:14.134 | INFO     | src.gui.main_dialogs:_on_target_changed:2541 | 数据期间自动同步为: 2025-12
2025-08-27 20:24:14.137 | INFO     | src.gui.main_dialogs:_on_target_changed:2561 | 数据描述自动生成: 2025年12月全部在职人员工资
2025-08-27 20:24:21.249 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-27 20:24:22.446 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-08-27 20:24:22.447 | INFO     | src.gui.main_dialogs:_auto_select_sheet_by_category:2651 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-08-27 20:24:32.837 | INFO     | src.gui.main_dialogs:_on_target_changed:2522 | 目标位置已更新: 异动人员表 > 2025年 > 12月 > 全部在职人员
2025-08-27 20:24:32.838 | INFO     | src.gui.main_dialogs:_on_change_mode_changed:2284 | 🔧 [P2-1修复] 异动表处理模式已切换为: 动态处理（保持原始）
2025-08-27 20:24:32.841 | INFO     | src.gui.main_dialogs:_on_target_changed:2541 | 数据期间自动同步为: 2025-12
2025-08-27 20:24:32.841 | INFO     | src.gui.main_dialogs:_on_target_changed:2561 | 数据描述自动生成: 2025年12月全部在职人员工资
2025-08-27 20:24:33.039 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-27 20:24:33.040 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-08-27 20:24:33.042 | INFO     | src.gui.main_dialogs:_auto_select_sheet_by_category:2651 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-08-27 20:24:36.134 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-27 20:24:36.134 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-27 20:24:36.138 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-27 20:24:36.266 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 23列 (列过滤: 否)
2025-08-27 20:24:36.270 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-27 20:24:36.273 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-08-27 20:24:36.273 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1397行 x 23列
2025-08-27 20:24:36.279 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-27 20:24:36.287 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-08-27 20:24:36.290 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1396行 × 23列
2025-08-27 20:24:36.351 | INFO     | src.modules.data_import.change_data_config_manager:__init__:43 | 配置管理器初始化完成，配置目录: C:\test\salary_changes\salary_changes\state\change_data_configs
2025-08-27 20:24:36.352 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: number
2025-08-27 20:24:36.354 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: string
2025-08-27 20:24:36.355 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: date
2025-08-27 20:24:36.356 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: code
2025-08-27 20:24:36.358 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: custom
2025-08-27 20:24:36.358 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: salary_float - 工资金额
2025-08-27 20:24:36.359 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: employee_id_string - 工号
2025-08-27 20:24:36.360 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: name_string - 姓名
2025-08-27 20:24:36.361 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: date_string - 日期
2025-08-27 20:24:36.361 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: id_number_string - 身份证号
2025-08-27 20:24:36.362 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: code_string - 代码
2025-08-27 20:24:36.368 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: float - 浮点数
2025-08-27 20:24:36.370 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: integer - 整数
2025-08-27 20:24:36.370 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: text_string - 文本字符串
2025-08-27 20:24:36.371 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: personnel_category_code - 人员类别代码
2025-08-27 20:24:36.372 | INFO     | src.modules.data_import.field_type_manager:__init__:41 | 字段类型管理器初始化完成，存储目录: C:\test\salary_changes\salary_changes\state\field_types
2025-08-27 20:24:36.891 | INFO     | src.gui.change_data_config_dialog:load_all_sheets:1438 | 成功加载 4 个工作表
2025-08-27 20:24:47.421 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:827 | 🔍 开始收集所有工作表的配置状态（包括未修改的表）...
2025-08-27 20:24:47.421 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:840 | 🔧 修复工作表名称：从 'unknown' 更正为 '离休人员工资表'
2025-08-27 20:24:47.426 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:847 | ✅ 收集到当前工作表 '离休人员工资表' 配置：23 个字段
2025-08-27 20:24:47.427 | INFO     | src.gui.change_data_config_dialog:_generate_default_config_for_sheet:947 | 为工作表 '退休人员工资表' 生成默认配置：27 个字段
2025-08-27 20:24:47.427 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:878 | 🆕 为未配置工作表 '退休人员工资表' 生成默认配置：27 个字段
2025-08-27 20:24:47.428 | INFO     | src.gui.change_data_config_dialog:_generate_default_config_for_sheet:947 | 为工作表 '全部在职人员工资表' 生成默认配置：23 个字段
2025-08-27 20:24:47.429 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:878 | 🆕 为未配置工作表 '全部在职人员工资表' 生成默认配置：23 个字段
2025-08-27 20:24:47.430 | INFO     | src.gui.change_data_config_dialog:_generate_default_config_for_sheet:947 | 为工作表 'A岗职工' 生成默认配置：21 个字段
2025-08-27 20:24:47.430 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:878 | 🆕 为未配置工作表 'A岗职工' 生成默认配置：21 个字段
2025-08-27 20:24:47.432 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:882 | 🎯 配置收集完成，共收集 4 个工作表的配置：
2025-08-27 20:24:47.432 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:885 |    - 离休人员工资表: 23 个字段
2025-08-27 20:24:47.434 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:885 |    - 退休人员工资表: 27 个字段
2025-08-27 20:24:47.440 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:885 |    - 全部在职人员工资表: 23 个字段
2025-08-27 20:24:47.441 | INFO     | src.gui.change_data_config_dialog:_collect_all_configured_sheets:885 |    - A岗职工: 21 个字段
2025-08-27 20:25:19.838 | INFO     | src.modules.data_import.change_data_config_manager:load_user_config:595 | 成功加载用户配置: tt1
2025-08-27 20:25:19.838 | INFO     | src.gui.change_data_config_dialog:load_selected_config:1122 | 尝试加载用户配置: tt1
2025-08-27 20:25:25.353 | WARNING  | src.gui.change_data_config_dialog:load_selected_config:1162 | 配置加载失败: user:tt1
2025-08-27 20:25:40.357 | INFO     | src.modules.data_import.change_data_config_manager:load_config:216 | 加载配置 'tt1'
2025-08-27 20:25:40.357 | INFO     | src.gui.change_data_config_dialog:load_selected_config:1127 | 尝试加载系统配置: tt1
2025-08-27 20:25:42.424 | WARNING  | src.gui.change_data_config_dialog:load_selected_config:1162 | 配置加载失败: system:tt1
2025-08-27 20:26:17.118 | INFO     | __main__:main:519 | 应用程序正常退出
2025-08-27 21:39:20.874 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-08-27 21:39:20.875 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-08-27 21:39:20.875 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-08-27 21:39:20.876 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-08-27 21:39:20.876 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-08-27 21:39:20.877 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-27 21:39:23.990 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-08-27 21:39:23.991 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-08-27 21:39:23.991 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-08-27 21:39:23.992 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-08-27 21:39:23.992 | INFO     | __main__:setup_app_logging:423 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-08-27 21:39:23.994 | INFO     | __main__:main:487 | 初始化核心管理器...
2025-08-27 21:39:23.994 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-27 21:39:23.995 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-27 21:39:23.995 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-27 21:39:23.996 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-27 21:39:23.997 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-27 21:39:24.008 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-27 21:39:24.009 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-08-27 21:39:24.010 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-27 21:39:24.018 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-27 21:39:24.019 | INFO     | __main__:main:492 | 核心管理器初始化完成。
2025-08-27 21:39:24.020 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-08-27 21:39:24.022 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-08-27 21:39:24.023 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-27 21:39:24.024 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-08-27 21:39:24.025 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-08-27 21:39:24.026 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-08-27 21:39:24.029 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-08-27 21:39:24.030 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:11817 | 🔧 [P2-3] 错误恢复策略注册完成
2025-08-27 21:39:24.033 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-27 21:39:24.034 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:11672 | 🔧 [P2-3] 错误处理机制设置完成
2025-08-27 21:39:24.034 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:11710 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-08-27 21:39:24.230 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-08-27 21:39:24.231 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-08-27 21:39:24.235 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-27 21:39:24.237 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-08-27 21:39:24.238 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-27 21:39:24.239 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-27 21:39:24.239 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-27 21:39:24.240 | INFO     | src.core.field_mapping_manager:_load_config:89 | 🔧 [P3优化] 字段映射配置加载成功
2025-08-27 21:39:24.241 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-08-27 21:39:24.242 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-27 21:39:24.248 | INFO     | src.modules.format_management.field_registry:__init__:95 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-08-27 21:39:24.249 | INFO     | src.core.unified_mapping_service:__init__:45 | UnifiedMappingService 初始化完成
2025-08-27 21:39:24.250 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-08-27 21:39:24.251 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-08-27 21:39:24.252 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-27 21:39:24.252 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-27 21:39:24.253 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-08-27 21:39:24.254 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 19.7ms
2025-08-27 21:39:24.274 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-08-27 21:39:24.274 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-08-27 21:39:24.277 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-08-27 21:39:24.277 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-08-27 21:39:24.278 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-08-27 21:39:24.279 | INFO     | src.gui.prototype.prototype_main_window:__init__:3650 | 🚀 性能管理器已集成
2025-08-27 21:39:24.281 | INFO     | src.gui.prototype.prototype_main_window:__init__:3652 | ✅ 新架构集成成功！
2025-08-27 21:39:24.283 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3765 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-08-27 21:39:24.284 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3730 | ✅ 新架构事件监听器设置完成
2025-08-27 21:39:24.292 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-08-27 21:39:24.293 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-08-27 21:39:24.294 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-08-27 21:39:24.564 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2726 | 菜单栏创建完成
2025-08-27 21:39:24.565 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-27 21:39:24.568 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: user_preferences.json
2025-08-27 21:39:24.570 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-08-27 21:39:24.573 | INFO     | src.gui.prototype.prototype_main_window:__init__:2701 | 菜单栏管理器初始化完成
2025-08-27 21:39:24.573 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-08-27 21:39:24.574 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5458 | 管理器设置完成，包含增强版表头管理器
2025-08-27 21:39:24.575 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5463 | 🔧 开始应用窗口级Material Design样式...
2025-08-27 21:39:24.575 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-08-27 21:39:24.578 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-08-27 21:39:24.582 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5470 | ✅ 窗口级样式应用成功
2025-08-27 21:39:24.582 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5511 | ✅ 响应式样式监听设置完成
2025-08-27 21:39:24.586 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-27 21:39:24.587 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-27 21:39:24.588 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-27 21:39:24.594 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-27 21:39:24.603 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-27 21:39:24.615 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-27 21:39:24.622 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1835 | 🏠 使用示例数据结构加载导航（首次启动或暂无数据时显示）
2025-08-27 21:39:24.624 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:2037 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-27 21:39:24.625 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-27 21:39:24.626 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:924 | 恢复导航状态: 0个展开项
2025-08-27 21:39:24.628 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月']
2025-08-27 21:39:24.629 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-27 21:39:24.630 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-27 21:39:24.634 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-27 21:39:24.642 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-27 21:39:24.644 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:2037 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-27 21:39:24.645 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-27 21:39:24.646 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-27 21:39:24.654 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-27 21:39:24.654 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 21:39:24.656 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1601 | 🔧 [P1-2修复] 检查表状态失败，1.0s后重试...
2025-08-27 21:39:24.657 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1405 | 🔧 [P1-2修复] 未找到最新工资数据路径（可能是首次启动）
2025-08-27 21:39:24.912 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-08-27 21:39:24.914 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2142 | 🚨 [架构修复] 启用模型数据同步机制
2025-08-27 21:39:24.919 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1366 | 快捷键注册完成: 18/18 个
2025-08-27 21:39:24.919 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1809 | 拖拽排序管理器初始化完成
2025-08-27 21:39:24.943 | INFO     | src.modules.data_management.data_flow_validator:__init__:85 | 🔧 [数据验证器] 初始化完成，验证级别: lenient
2025-08-27 21:39:24.943 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-08-27 21:39:24.946 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-08-27 21:39:24.948 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2195 | 🔧 [P0-紧急修复] 数据流验证器初始化成功（宽松模式）
2025-08-27 21:39:24.949 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-08-27 21:39:24.951 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-27 21:39:24.955 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-27 21:39:24.963 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2247 | 🎯 [统一格式管理] 使用统一格式管理器
2025-08-27 21:39:24.976 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:370 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-27 21:39:24.976 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-08-27 21:39:24.978 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2294 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-08-27 21:39:24.980 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1553 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-08-27 21:39:24.980 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1554 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-08-27 21:39:24.981 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1555 | 🔧 [列宽保存修复] 配置文件存在: True
2025-08-27 21:39:24.989 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1556 | 🔧 [列宽保存修复] 父目录存在: True
2025-08-27 21:39:24.990 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1557 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-08-27 21:39:24.991 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2301 | 列宽管理器初始化完成
2025-08-27 21:39:24.992 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2428 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-08-27 21:39:24.995 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2315 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-08-27 21:39:24.996 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-27 21:39:24.997 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-27 21:39:24.998 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-27 21:39:25.020 | INFO     | src.gui.prototype.widgets.pagination_state_manager:__init__:78 | 🔧 [P1优化] 分页状态管理器初始化完成
2025-08-27 21:39:25.021 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 0
2025-08-27 21:39:25.022 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2838 | 🔧 [P1优化] 表切换操作: None -> default_table
2025-08-27 21:39:25.023 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-27 21:39:25.033 | INFO     | src.gui.prototype.widgets.table_header_cache:__init__:67 | 🔧 [P1优化] 表头缓存管理器初始化完成，最大缓存: 100
2025-08-27 21:39:25.033 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2890 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-27 21:39:25.034 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-27 21:39:25.054 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-27 21:39:25.058 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-27 21:39:25.059 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-27 21:39:25.060 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-27 21:39:25.060 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-27 21:39:25.064 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-08-27 21:39:25.065 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 59.8ms
2025-08-27 21:39:25.072 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-27 21:39:25.072 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-27 21:39:25.081 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-27 21:39:25.084 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-27 21:39:25.085 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-27 21:39:25.096 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-08-27 21:39:25.113 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-08-27 21:39:25.115 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-08-27 21:39:25.168 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:594 | 控制面板按钮信号连接完成
2025-08-27 21:39:25.216 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-27 21:39:25.216 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-27 21:39:25.219 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5420 | 快捷键设置完成
2025-08-27 21:39:25.220 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5377 | 主窗口UI设置完成。
2025-08-27 21:39:25.221 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5614 | 🔧 [全局排序] 全局排序开关连接成功
2025-08-27 21:39:25.222 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5646 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-08-27 21:39:25.222 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5658 | ✅ 已连接分页刷新信号到主窗口
2025-08-27 21:39:25.223 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5659 | ✅ 已连接分页组件事件到新架构
2025-08-27 21:39:25.227 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5670 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-08-27 21:39:25.227 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5673 | 信号连接设置完成
2025-08-27 21:39:25.234 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6849 | 🔧 [P1-2修复] 发现 2 个表的配置
2025-08-27 21:39:25.236 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: salary_data_2025_07_active_employees, 10个字段
2025-08-27 21:39:25.237 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: active_employees, 5个字段
2025-08-27 21:39:25.238 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6859 | ✅ [P1-2修复] 已加载字段映射信息，共2个表的映射
2025-08-27 21:39:25.249 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-27 21:39:25.250 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-27 21:39:25.252 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-27 21:39:25.254 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-27 21:39:25.255 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-27 21:39:25.257 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 22
2025-08-27 21:39:25.264 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-27 21:39:25.265 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-27 21:39:25.266 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-27 21:39:25.267 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-27 21:39:25.268 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 14.7ms
2025-08-27 21:39:25.269 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-27 21:39:25.270 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-27 21:39:25.271 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-27 21:39:25.276 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-27 21:39:25.278 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-27 21:39:25.279 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-27 21:39:25.280 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8551 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-08-27 21:39:25.281 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-27 21:39:25.281 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-27 21:39:25.282 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-27 21:39:25.283 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-27 21:39:25.284 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-27 21:39:25.291 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-27 21:39:25.291 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-27 21:39:25.292 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-27 21:39:25.294 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-27 21:39:25.295 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 11.6ms
2025-08-27 21:39:25.296 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-27 21:39:25.298 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-27 21:39:25.299 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-27 21:39:25.305 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-27 21:39:25.305 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-27 21:39:25.306 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8569 | 已显示标准空表格，表头数量: 22
2025-08-27 21:39:25.307 | INFO     | src.gui.prototype.prototype_main_window:__init__:3704 | 原型主窗口初始化完成
2025-08-27 21:39:25.343 | INFO     | __main__:main:514 | 应用程序启动成功
2025-08-27 21:39:25.389 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-27 21:39:25.496 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-27 21:39:25.498 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-27 21:39:25.502 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1474 | 执行延迟的自动选择最新数据...
2025-08-27 21:39:25.503 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 21:39:25.506 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1497 | 延迟自动选择最新数据失败，可能没有可用数据
2025-08-27 21:39:25.544 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-08-27 21:39:25.545 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2079 | MainWorkspaceArea 响应式适配: sm
2025-08-27 21:39:25.677 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 21:39:26.053 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9453 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet']
2025-08-27 21:39:26.054 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9363 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-27 21:39:26.057 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9377 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet']
2025-08-27 21:39:26.057 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:9911 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet']
2025-08-27 21:39:26.073 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9383 | 🔧 [P0-1] 智能显示亮度修复完成
2025-08-27 21:39:26.507 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 21:39:26.507 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1612 | 🔧 [P1-2修复] 3次重试已用尽，可能数据导入尚未完成
2025-08-27 21:39:36.179 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:654 | 数据导入功能被触发，发出 import_requested 信号。
2025-08-27 21:39:36.180 | INFO     | src.gui.prototype.prototype_main_window:_get_suggested_target_path:8374 | 检测到当前在工资表TAB，生成工资表默认路径
2025-08-27 21:39:36.183 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5906 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 08月 > 全部在职人员。打开导入对话框。
2025-08-27 21:39:36.184 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-08-27 21:39:36.184 | INFO     | src.modules.data_import.change_data_config_manager:__init__:43 | 配置管理器初始化完成，配置目录: C:\test\salary_changes\salary_changes\state\change_data_configs
2025-08-27 21:39:36.187 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:78 | 多Sheet导入器初始化完成
2025-08-27 21:39:36.189 | INFO     | src.gui.widgets.target_selection_widget:_load_available_options:250 | 成功加载导航配置
2025-08-27 21:39:36.202 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-27 21:39:36.250 | INFO     | src.gui.main_dialogs:_get_template_fields:2010 | 🔧 [P3-修复] ConfigSyncManager未初始化，使用默认字段模板
2025-08-27 21:39:36.251 | INFO     | src.gui.main_dialogs:_init_field_mapping:1997 | 🔧 [P3-修复] 未找到字段模板，使用内置默认字段列表
2025-08-27 21:39:36.313 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-08-27 21:39:36.313 | INFO     | src.modules.data_import.import_defaults_manager:get_smart_defaults_for_category:244 | 检测到工资表导入，设置table_template为salary_data
2025-08-27 21:39:36.317 | INFO     | src.gui.main_dialogs:_apply_default_settings:2616 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'separate_tables', 'table_template': 'salary_data'}
2025-08-27 21:39:36.317 | INFO     | src.gui.main_dialogs:_setup_tooltips:2989 | 工具提示设置完成
2025-08-27 21:39:36.319 | INFO     | src.gui.main_dialogs:_setup_shortcuts:3028 | 快捷键设置完成
2025-08-27 21:39:36.319 | INFO     | src.gui.main_dialogs:__init__:82 | 数据导入对话框初始化完成。
2025-08-27 21:39:36.320 | INFO     | src.modules.data_import.multi_sheet_importer:set_config_sync_manager:87 | 🔧 [修复] ConfigSyncManager已设置到MultiSheetImporter
2025-08-27 21:39:36.321 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5917 | 🔧 [P0-修复] ConfigSyncManager已设置到数据导入对话框
2025-08-27 21:39:38.651 | INFO     | src.gui.main_dialogs:_on_target_changed:2522 | 目标位置已更新: 异动人员表 > 2025年 > 08月 > 全部在职人员
2025-08-27 21:39:38.654 | INFO     | src.gui.main_dialogs:_on_change_mode_changed:2284 | 🔧 [P2-1修复] 异动表处理模式已切换为: 动态处理（保持原始）
2025-08-27 21:39:38.657 | INFO     | src.gui.main_dialogs:_on_target_changed:2541 | 数据期间自动同步为: 2025-08
2025-08-27 21:39:38.657 | INFO     | src.gui.main_dialogs:_on_target_changed:2561 | 数据描述自动生成: 2025年08月全部在职人员工资
2025-08-27 21:39:41.715 | INFO     | src.gui.main_dialogs:_on_target_changed:2522 | 目标位置已更新: 异动人员表 > 2025年 > 12月 > 全部在职人员
2025-08-27 21:39:41.716 | INFO     | src.gui.main_dialogs:_on_target_changed:2541 | 数据期间自动同步为: 2025-12
2025-08-27 21:39:41.719 | INFO     | src.gui.main_dialogs:_on_target_changed:2561 | 数据描述自动生成: 2025年12月全部在职人员工资
2025-08-27 21:39:52.014 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-27 21:39:53.567 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-08-27 21:39:53.569 | INFO     | src.gui.main_dialogs:_auto_select_sheet_by_category:2651 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-08-27 21:40:00.660 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-27 21:40:00.661 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-27 21:40:00.664 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-27 21:40:00.792 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 23列 (列过滤: 否)
2025-08-27 21:40:00.796 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-27 21:40:00.798 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-08-27 21:40:00.799 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1397行 x 23列
2025-08-27 21:40:00.804 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-27 21:40:00.808 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-08-27 21:40:00.811 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1396行 × 23列
2025-08-27 21:40:00.862 | INFO     | src.modules.data_import.change_data_config_manager:__init__:43 | 配置管理器初始化完成，配置目录: C:\test\salary_changes\salary_changes\state\change_data_configs
2025-08-27 21:40:00.863 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: number
2025-08-27 21:40:00.866 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: string
2025-08-27 21:40:00.867 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: date
2025-08-27 21:40:00.869 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: code
2025-08-27 21:40:00.870 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: custom
2025-08-27 21:40:00.870 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: salary_float - 工资金额
2025-08-27 21:40:00.871 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: employee_id_string - 工号
2025-08-27 21:40:00.872 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: name_string - 姓名
2025-08-27 21:40:00.873 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: date_string - 日期
2025-08-27 21:40:00.874 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: id_number_string - 身份证号
2025-08-27 21:40:00.883 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: code_string - 代码
2025-08-27 21:40:00.884 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: float - 浮点数
2025-08-27 21:40:00.884 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: integer - 整数
2025-08-27 21:40:00.885 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: text_string - 文本字符串
2025-08-27 21:40:00.885 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: personnel_category_code - 人员类别代码
2025-08-27 21:40:00.887 | INFO     | src.modules.data_import.field_type_manager:__init__:41 | 字段类型管理器初始化完成，存储目录: C:\test\salary_changes\salary_changes\state\field_types
2025-08-27 21:40:01.415 | INFO     | src.gui.change_data_config_dialog:load_all_sheets:1548 | 成功加载 4 个工作表
2025-08-27 21:40:40.195 | INFO     | src.modules.data_import.change_data_config_manager:load_config:216 | 加载配置 'tt1'
2025-08-27 21:40:40.197 | INFO     | src.gui.change_data_config_dialog:load_selected_config:1127 | 尝试加载系统配置: tt1
2025-08-27 21:40:42.799 | WARNING  | src.gui.change_data_config_dialog:load_selected_config:1162 | 配置加载失败: system:tt1
2025-08-27 21:42:06.260 | INFO     | src.modules.data_import.change_data_config_manager:load_user_config:595 | 成功加载用户配置: tt1
2025-08-27 21:42:06.261 | INFO     | src.gui.change_data_config_dialog:load_selected_config:1122 | 尝试加载用户配置: tt1
2025-08-27 21:42:08.696 | WARNING  | src.gui.change_data_config_dialog:load_selected_config:1162 | 配置加载失败: user:tt1
2025-08-27 21:42:27.564 | INFO     | src.modules.data_import.change_data_config_manager:load_config:216 | 加载配置 'tt1'
2025-08-27 21:42:27.565 | INFO     | src.gui.change_data_config_dialog:load_selected_config:1127 | 尝试加载系统配置: tt1
2025-08-27 21:42:50.964 | WARNING  | src.gui.change_data_config_dialog:load_selected_config:1162 | 配置加载失败: system:tt1
2025-08-27 21:45:10.322 | ERROR    | src.gui.change_data_config_dialog:import_configuration:1368 | 导入配置时发生错误: 'QWidget' object has no attribute 'count'
2025-08-27 21:46:42.367 | INFO     | __main__:main:519 | 应用程序正常退出
2025-08-27 22:03:13.138 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-08-27 22:03:13.139 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-08-27 22:03:13.139 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-08-27 22:03:13.139 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-08-27 22:03:13.140 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-08-27 22:03:13.142 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-27 22:03:16.445 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-08-27 22:03:16.445 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-08-27 22:03:16.448 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-08-27 22:03:16.449 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-08-27 22:03:16.450 | INFO     | __main__:setup_app_logging:423 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-08-27 22:03:16.450 | INFO     | __main__:main:487 | 初始化核心管理器...
2025-08-27 22:03:16.450 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-27 22:03:16.451 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-27 22:03:16.452 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-27 22:03:16.453 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-27 22:03:16.454 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-27 22:03:16.467 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-27 22:03:16.468 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-08-27 22:03:16.469 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-27 22:03:16.473 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-27 22:03:16.476 | INFO     | __main__:main:492 | 核心管理器初始化完成。
2025-08-27 22:03:16.476 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-08-27 22:03:16.476 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-08-27 22:03:16.476 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-27 22:03:16.476 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-08-27 22:03:16.476 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-08-27 22:03:16.476 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-08-27 22:03:16.476 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-08-27 22:03:16.476 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:11817 | 🔧 [P2-3] 错误恢复策略注册完成
2025-08-27 22:03:16.476 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-27 22:03:16.476 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:11672 | 🔧 [P2-3] 错误处理机制设置完成
2025-08-27 22:03:16.476 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:11710 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-08-27 22:03:16.698 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-08-27 22:03:16.699 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-08-27 22:03:16.702 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-27 22:03:16.704 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-08-27 22:03:16.706 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-27 22:03:16.707 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-27 22:03:16.708 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-27 22:03:16.709 | INFO     | src.core.field_mapping_manager:_load_config:89 | 🔧 [P3优化] 字段映射配置加载成功
2025-08-27 22:03:16.713 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-08-27 22:03:16.718 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-27 22:03:16.719 | INFO     | src.modules.format_management.field_registry:__init__:95 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-08-27 22:03:16.720 | INFO     | src.core.unified_mapping_service:__init__:45 | UnifiedMappingService 初始化完成
2025-08-27 22:03:16.720 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-08-27 22:03:16.721 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-08-27 22:03:16.724 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-27 22:03:16.727 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-27 22:03:16.732 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-08-27 22:03:16.733 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 31.2ms
2025-08-27 22:03:16.754 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-08-27 22:03:16.754 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-08-27 22:03:16.754 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-08-27 22:03:16.754 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-08-27 22:03:16.754 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-08-27 22:03:16.754 | INFO     | src.gui.prototype.prototype_main_window:__init__:3650 | 🚀 性能管理器已集成
2025-08-27 22:03:16.754 | INFO     | src.gui.prototype.prototype_main_window:__init__:3652 | ✅ 新架构集成成功！
2025-08-27 22:03:16.754 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3765 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-08-27 22:03:16.754 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3730 | ✅ 新架构事件监听器设置完成
2025-08-27 22:03:16.754 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-08-27 22:03:16.770 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-08-27 22:03:16.770 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-08-27 22:03:17.043 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2726 | 菜单栏创建完成
2025-08-27 22:03:17.044 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-27 22:03:17.047 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: user_preferences.json
2025-08-27 22:03:17.048 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-08-27 22:03:17.052 | INFO     | src.gui.prototype.prototype_main_window:__init__:2701 | 菜单栏管理器初始化完成
2025-08-27 22:03:17.053 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-08-27 22:03:17.053 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5458 | 管理器设置完成，包含增强版表头管理器
2025-08-27 22:03:17.054 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5463 | 🔧 开始应用窗口级Material Design样式...
2025-08-27 22:03:17.054 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-08-27 22:03:17.058 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-08-27 22:03:17.061 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5470 | ✅ 窗口级样式应用成功
2025-08-27 22:03:17.062 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5511 | ✅ 响应式样式监听设置完成
2025-08-27 22:03:17.066 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-27 22:03:17.068 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-27 22:03:17.071 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-27 22:03:17.080 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:165 | 导航状态加载成功
2025-08-27 22:03:17.080 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-27 22:03:17.085 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-27 22:03:17.099 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-27 22:03:17.099 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1835 | 🏠 使用示例数据结构加载导航（首次启动或暂无数据时显示）
2025-08-27 22:03:17.099 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:2037 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-27 22:03:17.099 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-27 22:03:17.114 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:924 | 恢复导航状态: 1个展开项
2025-08-27 22:03:17.114 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月']
2025-08-27 22:03:17.125 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-27 22:03:17.126 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-27 22:03:17.130 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-27 22:03:17.139 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-27 22:03:17.144 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:2037 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-27 22:03:17.149 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-27 22:03:17.152 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-27 22:03:17.155 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-27 22:03:17.164 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 22:03:17.165 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1601 | 🔧 [P1-2修复] 检查表状态失败，1.0s后重试...
2025-08-27 22:03:17.166 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1405 | 🔧 [P1-2修复] 未找到最新工资数据路径（可能是首次启动）
2025-08-27 22:03:17.425 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-08-27 22:03:17.427 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2142 | 🚨 [架构修复] 启用模型数据同步机制
2025-08-27 22:03:17.431 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1366 | 快捷键注册完成: 18/18 个
2025-08-27 22:03:17.435 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1809 | 拖拽排序管理器初始化完成
2025-08-27 22:03:17.458 | INFO     | src.modules.data_management.data_flow_validator:__init__:85 | 🔧 [数据验证器] 初始化完成，验证级别: lenient
2025-08-27 22:03:17.459 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-08-27 22:03:17.464 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-08-27 22:03:17.464 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2195 | 🔧 [P0-紧急修复] 数据流验证器初始化成功（宽松模式）
2025-08-27 22:03:17.468 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-08-27 22:03:17.468 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-27 22:03:17.469 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-27 22:03:17.470 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2247 | 🎯 [统一格式管理] 使用统一格式管理器
2025-08-27 22:03:17.492 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:370 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-27 22:03:17.495 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-08-27 22:03:17.497 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2294 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-08-27 22:03:17.502 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1553 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-08-27 22:03:17.503 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1554 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-08-27 22:03:17.504 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1555 | 🔧 [列宽保存修复] 配置文件存在: True
2025-08-27 22:03:17.505 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1556 | 🔧 [列宽保存修复] 父目录存在: True
2025-08-27 22:03:17.506 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1557 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-08-27 22:03:17.507 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2301 | 列宽管理器初始化完成
2025-08-27 22:03:17.508 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2428 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-08-27 22:03:17.514 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2315 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-08-27 22:03:17.517 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-27 22:03:17.517 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-27 22:03:17.518 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-27 22:03:17.532 | INFO     | src.gui.prototype.widgets.pagination_state_manager:__init__:78 | 🔧 [P1优化] 分页状态管理器初始化完成
2025-08-27 22:03:17.533 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 0
2025-08-27 22:03:17.535 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2838 | 🔧 [P1优化] 表切换操作: None -> default_table
2025-08-27 22:03:17.535 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-27 22:03:17.545 | INFO     | src.gui.prototype.widgets.table_header_cache:__init__:67 | 🔧 [P1优化] 表头缓存管理器初始化完成，最大缓存: 100
2025-08-27 22:03:17.546 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2890 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-27 22:03:17.547 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-27 22:03:17.565 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-27 22:03:17.568 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-27 22:03:17.569 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-27 22:03:17.573 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-27 22:03:17.575 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-27 22:03:17.578 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-08-27 22:03:17.583 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 59.7ms
2025-08-27 22:03:17.588 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-27 22:03:17.604 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-27 22:03:17.609 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-27 22:03:17.609 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-27 22:03:17.610 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-27 22:03:17.641 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-08-27 22:03:17.654 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-08-27 22:03:17.655 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-08-27 22:03:17.713 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:594 | 控制面板按钮信号连接完成
2025-08-27 22:03:17.763 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-27 22:03:17.764 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-27 22:03:17.773 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5420 | 快捷键设置完成
2025-08-27 22:03:17.774 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5377 | 主窗口UI设置完成。
2025-08-27 22:03:17.776 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5614 | 🔧 [全局排序] 全局排序开关连接成功
2025-08-27 22:03:17.777 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5646 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-08-27 22:03:17.778 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5658 | ✅ 已连接分页刷新信号到主窗口
2025-08-27 22:03:17.778 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5659 | ✅ 已连接分页组件事件到新架构
2025-08-27 22:03:17.780 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5670 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-08-27 22:03:17.786 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5673 | 信号连接设置完成
2025-08-27 22:03:17.787 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6849 | 🔧 [P1-2修复] 发现 2 个表的配置
2025-08-27 22:03:17.788 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: salary_data_2025_07_active_employees, 10个字段
2025-08-27 22:03:17.789 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: active_employees, 5个字段
2025-08-27 22:03:17.790 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6859 | ✅ [P1-2修复] 已加载字段映射信息，共2个表的映射
2025-08-27 22:03:17.802 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-27 22:03:17.805 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-27 22:03:17.805 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-27 22:03:17.806 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-27 22:03:17.807 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-27 22:03:17.808 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 22
2025-08-27 22:03:17.809 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-27 22:03:17.810 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-27 22:03:17.811 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-27 22:03:17.812 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-27 22:03:17.816 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 9.9ms
2025-08-27 22:03:17.821 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-27 22:03:17.822 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-27 22:03:17.823 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-27 22:03:17.824 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-27 22:03:17.825 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-27 22:03:17.828 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-27 22:03:17.830 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8551 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-08-27 22:03:17.836 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-27 22:03:17.837 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-27 22:03:17.838 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-27 22:03:17.839 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-27 22:03:17.840 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-27 22:03:17.841 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-27 22:03:17.842 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-27 22:03:17.842 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-27 22:03:17.846 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-27 22:03:17.851 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 12.0ms
2025-08-27 22:03:17.852 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-27 22:03:17.853 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-27 22:03:17.855 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-27 22:03:17.856 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-27 22:03:17.856 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-27 22:03:17.862 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8569 | 已显示标准空表格，表头数量: 22
2025-08-27 22:03:17.890 | INFO     | src.gui.prototype.prototype_main_window:__init__:3704 | 原型主窗口初始化完成
2025-08-27 22:03:17.960 | INFO     | __main__:main:514 | 应用程序启动成功
2025-08-27 22:03:17.971 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-27 22:03:17.972 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1474 | 执行延迟的自动选择最新数据...
2025-08-27 22:03:17.977 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-27 22:03:17.978 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 22:03:17.980 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1497 | 延迟自动选择最新数据失败，可能没有可用数据
2025-08-27 22:03:17.985 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-27 22:03:18.144 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-08-27 22:03:18.144 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2079 | MainWorkspaceArea 响应式适配: sm
2025-08-27 22:03:18.235 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 22:03:18.654 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9453 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet']
2025-08-27 22:03:18.654 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9363 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-27 22:03:18.658 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9377 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet']
2025-08-27 22:03:18.659 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:9911 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet']
2025-08-27 22:03:18.675 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9383 | 🔧 [P0-1] 智能显示亮度修复完成
2025-08-27 22:03:18.980 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 22:03:18.981 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1612 | 🔧 [P1-2修复] 3次重试已用尽，可能数据导入尚未完成
2025-08-27 22:06:49.588 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:654 | 数据导入功能被触发，发出 import_requested 信号。
2025-08-27 22:06:49.589 | INFO     | src.gui.prototype.prototype_main_window:_get_suggested_target_path:8374 | 检测到当前在工资表TAB，生成工资表默认路径
2025-08-27 22:06:49.593 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5906 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 08月 > 全部在职人员。打开导入对话框。
2025-08-27 22:06:49.594 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-08-27 22:06:49.595 | INFO     | src.modules.data_import.change_data_config_manager:__init__:43 | 配置管理器初始化完成，配置目录: C:\test\salary_changes\salary_changes\state\change_data_configs
2025-08-27 22:06:49.596 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:78 | 多Sheet导入器初始化完成
2025-08-27 22:06:49.600 | INFO     | src.gui.widgets.target_selection_widget:_load_available_options:250 | 成功加载导航配置
2025-08-27 22:06:49.612 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-27 22:06:49.659 | INFO     | src.gui.main_dialogs:_get_template_fields:2010 | 🔧 [P3-修复] ConfigSyncManager未初始化，使用默认字段模板
2025-08-27 22:06:49.660 | INFO     | src.gui.main_dialogs:_init_field_mapping:1997 | 🔧 [P3-修复] 未找到字段模板，使用内置默认字段列表
2025-08-27 22:06:49.732 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-08-27 22:06:49.733 | INFO     | src.modules.data_import.import_defaults_manager:get_smart_defaults_for_category:244 | 检测到工资表导入，设置table_template为salary_data
2025-08-27 22:06:49.736 | INFO     | src.gui.main_dialogs:_apply_default_settings:2616 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'separate_tables', 'table_template': 'salary_data'}
2025-08-27 22:06:49.736 | INFO     | src.gui.main_dialogs:_setup_tooltips:2989 | 工具提示设置完成
2025-08-27 22:06:49.738 | INFO     | src.gui.main_dialogs:_setup_shortcuts:3028 | 快捷键设置完成
2025-08-27 22:06:49.739 | INFO     | src.gui.main_dialogs:__init__:82 | 数据导入对话框初始化完成。
2025-08-27 22:06:49.739 | INFO     | src.modules.data_import.multi_sheet_importer:set_config_sync_manager:87 | 🔧 [修复] ConfigSyncManager已设置到MultiSheetImporter
2025-08-27 22:06:49.739 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5917 | 🔧 [P0-修复] ConfigSyncManager已设置到数据导入对话框
2025-08-27 22:06:51.885 | INFO     | src.gui.main_dialogs:_on_target_changed:2522 | 目标位置已更新: 异动人员表 > 2025年 > 08月 > 全部在职人员
2025-08-27 22:06:51.886 | INFO     | src.gui.main_dialogs:_on_change_mode_changed:2284 | 🔧 [P2-1修复] 异动表处理模式已切换为: 动态处理（保持原始）
2025-08-27 22:06:51.888 | INFO     | src.gui.main_dialogs:_on_target_changed:2541 | 数据期间自动同步为: 2025-08
2025-08-27 22:06:51.889 | INFO     | src.gui.main_dialogs:_on_target_changed:2561 | 数据描述自动生成: 2025年08月全部在职人员工资
2025-08-27 22:06:54.004 | INFO     | src.gui.main_dialogs:_on_target_changed:2522 | 目标位置已更新: 异动人员表 > 2025年 > 12月 > 全部在职人员
2025-08-27 22:06:54.008 | INFO     | src.gui.main_dialogs:_on_target_changed:2541 | 数据期间自动同步为: 2025-12
2025-08-27 22:06:54.012 | INFO     | src.gui.main_dialogs:_on_target_changed:2561 | 数据描述自动生成: 2025年12月全部在职人员工资
2025-08-27 22:07:00.547 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-27 22:07:01.807 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-08-27 22:07:01.807 | INFO     | src.gui.main_dialogs:_auto_select_sheet_by_category:2651 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-08-27 22:07:05.621 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-27 22:07:05.622 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-27 22:07:05.625 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-27 22:07:05.760 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 23列 (列过滤: 否)
2025-08-27 22:07:05.764 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-27 22:07:05.767 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-08-27 22:07:05.767 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1397行 x 23列
2025-08-27 22:07:05.774 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-27 22:07:05.778 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-08-27 22:07:05.780 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1396行 × 23列
2025-08-27 22:07:05.839 | INFO     | src.modules.data_import.change_data_config_manager:__init__:43 | 配置管理器初始化完成，配置目录: C:\test\salary_changes\salary_changes\state\change_data_configs
2025-08-27 22:07:05.839 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: number
2025-08-27 22:07:05.842 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: string
2025-08-27 22:07:05.844 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: date
2025-08-27 22:07:05.845 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: code
2025-08-27 22:07:05.845 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: custom
2025-08-27 22:07:05.846 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: salary_float - 工资金额
2025-08-27 22:07:05.847 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: employee_id_string - 工号
2025-08-27 22:07:05.847 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: name_string - 姓名
2025-08-27 22:07:05.848 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: date_string - 日期
2025-08-27 22:07:05.849 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: id_number_string - 身份证号
2025-08-27 22:07:05.849 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: code_string - 代码
2025-08-27 22:07:05.849 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: float - 浮点数
2025-08-27 22:07:05.852 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: integer - 整数
2025-08-27 22:07:05.858 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: text_string - 文本字符串
2025-08-27 22:07:05.858 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: personnel_category_code - 人员类别代码
2025-08-27 22:07:05.862 | INFO     | src.modules.data_import.field_type_manager:__init__:41 | 字段类型管理器初始化完成，存储目录: C:\test\salary_changes\salary_changes\state\field_types
2025-08-27 22:07:06.400 | INFO     | src.gui.change_data_config_dialog:load_all_sheets:1590 | 成功加载 4 个工作表
2025-08-27 22:07:38.201 | INFO     | src.gui.change_data_config_dialog:load_selected_config:1127 | 尝试加载系统配置: tt1
2025-08-27 22:07:38.202 | ERROR    | src.gui.change_data_config_dialog:load_selected_config:1164 | 加载配置时发生错误: 'QWidget' object has no attribute 'count'
2025-08-27 22:07:56.317 | INFO     | src.modules.data_import.change_data_config_manager:load_user_config:611 | 成功加载用户配置: tt1
2025-08-27 22:07:56.318 | INFO     | src.gui.change_data_config_dialog:load_selected_config:1122 | 尝试加载用户配置: tt1
2025-08-27 22:07:58.088 | WARNING  | src.gui.change_data_config_dialog:load_selected_config:1162 | 配置加载失败: user:tt1
2025-08-27 22:08:05.320 | INFO     | src.gui.change_data_config_dialog:load_selected_config:1127 | 尝试加载系统配置: tt1
2025-08-27 22:08:05.322 | ERROR    | src.gui.change_data_config_dialog:load_selected_config:1164 | 加载配置时发生错误: 'QWidget' object has no attribute 'count'
2025-08-27 22:09:06.278 | INFO     | src.modules.data_import.change_data_config_manager:load_user_config:611 | 成功加载用户配置: tt1
2025-08-27 22:09:06.278 | INFO     | src.gui.change_data_config_dialog:load_selected_config:1122 | 尝试加载用户配置: tt1
2025-08-27 22:09:07.528 | WARNING  | src.gui.change_data_config_dialog:load_selected_config:1162 | 配置加载失败: user:tt1
2025-08-27 22:09:43.426 | INFO     | src.gui.change_data_config_dialog:load_selected_config:1127 | 尝试加载系统配置: tt1
2025-08-27 22:09:43.428 | ERROR    | src.gui.change_data_config_dialog:load_selected_config:1164 | 加载配置时发生错误: 'QWidget' object has no attribute 'count'
2025-08-27 22:14:48.045 | INFO     | __main__:main:519 | 应用程序正常退出
2025-08-27 22:19:56.771 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-08-27 22:19:56.772 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-08-27 22:19:56.772 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-08-27 22:19:56.773 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-08-27 22:19:56.775 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-08-27 22:19:56.779 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-27 22:19:59.991 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-08-27 22:19:59.992 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-08-27 22:19:59.993 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-08-27 22:19:59.993 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-08-27 22:19:59.994 | INFO     | __main__:setup_app_logging:423 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-08-27 22:19:59.994 | INFO     | __main__:main:487 | 初始化核心管理器...
2025-08-27 22:19:59.994 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-27 22:19:59.995 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-27 22:19:59.996 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-27 22:19:59.996 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-27 22:19:59.996 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-27 22:20:00.016 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-27 22:20:00.017 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-08-27 22:20:00.019 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-27 22:20:00.026 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-27 22:20:00.029 | INFO     | __main__:main:492 | 核心管理器初始化完成。
2025-08-27 22:20:00.033 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-08-27 22:20:00.035 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-08-27 22:20:00.036 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-27 22:20:00.037 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-08-27 22:20:00.038 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-08-27 22:20:00.038 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-08-27 22:20:00.041 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-08-27 22:20:00.044 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:11817 | 🔧 [P2-3] 错误恢复策略注册完成
2025-08-27 22:20:00.049 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-27 22:20:00.050 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:11672 | 🔧 [P2-3] 错误处理机制设置完成
2025-08-27 22:20:00.051 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:11710 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-08-27 22:20:00.265 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-08-27 22:20:00.266 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-08-27 22:20:00.270 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-27 22:20:00.271 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-08-27 22:20:00.274 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-27 22:20:00.275 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-27 22:20:00.275 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-27 22:20:00.276 | INFO     | src.core.field_mapping_manager:_load_config:89 | 🔧 [P3优化] 字段映射配置加载成功
2025-08-27 22:20:00.278 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-08-27 22:20:00.283 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-27 22:20:00.285 | INFO     | src.modules.format_management.field_registry:__init__:95 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-08-27 22:20:00.288 | INFO     | src.core.unified_mapping_service:__init__:45 | UnifiedMappingService 初始化完成
2025-08-27 22:20:00.290 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-08-27 22:20:00.290 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-08-27 22:20:00.296 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-27 22:20:00.297 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-27 22:20:00.298 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-08-27 22:20:00.300 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 31.4ms
2025-08-27 22:20:00.336 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-08-27 22:20:00.337 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-08-27 22:20:00.342 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-08-27 22:20:00.344 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-08-27 22:20:00.346 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-08-27 22:20:00.346 | INFO     | src.gui.prototype.prototype_main_window:__init__:3650 | 🚀 性能管理器已集成
2025-08-27 22:20:00.346 | INFO     | src.gui.prototype.prototype_main_window:__init__:3652 | ✅ 新架构集成成功！
2025-08-27 22:20:00.346 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3765 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-08-27 22:20:00.346 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3730 | ✅ 新架构事件监听器设置完成
2025-08-27 22:20:00.346 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-08-27 22:20:00.346 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-08-27 22:20:00.346 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-08-27 22:20:00.611 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2726 | 菜单栏创建完成
2025-08-27 22:20:00.612 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-27 22:20:00.615 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: user_preferences.json
2025-08-27 22:20:00.616 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-08-27 22:20:00.619 | INFO     | src.gui.prototype.prototype_main_window:__init__:2701 | 菜单栏管理器初始化完成
2025-08-27 22:20:00.623 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-08-27 22:20:00.627 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5458 | 管理器设置完成，包含增强版表头管理器
2025-08-27 22:20:00.628 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5463 | 🔧 开始应用窗口级Material Design样式...
2025-08-27 22:20:00.629 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-08-27 22:20:00.633 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-08-27 22:20:00.634 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5470 | ✅ 窗口级样式应用成功
2025-08-27 22:20:00.635 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5511 | ✅ 响应式样式监听设置完成
2025-08-27 22:20:00.640 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-27 22:20:00.644 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-27 22:20:00.646 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-27 22:20:00.648 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:165 | 导航状态加载成功
2025-08-27 22:20:00.648 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-27 22:20:00.655 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-27 22:20:00.666 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-27 22:20:00.676 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1835 | 🏠 使用示例数据结构加载导航（首次启动或暂无数据时显示）
2025-08-27 22:20:00.677 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:2037 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-27 22:20:00.678 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-27 22:20:00.690 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:924 | 恢复导航状态: 1个展开项
2025-08-27 22:20:00.691 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月']
2025-08-27 22:20:00.692 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-27 22:20:00.694 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-27 22:20:00.697 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-27 22:20:00.704 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-27 22:20:00.706 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:2037 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-27 22:20:00.707 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-27 22:20:00.708 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-27 22:20:00.709 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-27 22:20:00.712 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 22:20:00.715 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1601 | 🔧 [P1-2修复] 检查表状态失败，1.0s后重试...
2025-08-27 22:20:00.720 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1405 | 🔧 [P1-2修复] 未找到最新工资数据路径（可能是首次启动）
2025-08-27 22:20:00.856 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-08-27 22:20:00.857 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2142 | 🚨 [架构修复] 启用模型数据同步机制
2025-08-27 22:20:00.861 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1366 | 快捷键注册完成: 18/18 个
2025-08-27 22:20:00.865 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1809 | 拖拽排序管理器初始化完成
2025-08-27 22:20:00.897 | INFO     | src.modules.data_management.data_flow_validator:__init__:85 | 🔧 [数据验证器] 初始化完成，验证级别: lenient
2025-08-27 22:20:00.899 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-08-27 22:20:00.903 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-08-27 22:20:00.904 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2195 | 🔧 [P0-紧急修复] 数据流验证器初始化成功（宽松模式）
2025-08-27 22:20:00.905 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-08-27 22:20:00.905 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-27 22:20:00.906 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-27 22:20:00.907 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2247 | 🎯 [统一格式管理] 使用统一格式管理器
2025-08-27 22:20:00.921 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:370 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-27 22:20:00.922 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-08-27 22:20:00.925 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2294 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-08-27 22:20:00.932 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1553 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-08-27 22:20:00.932 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1554 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-08-27 22:20:00.933 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1555 | 🔧 [列宽保存修复] 配置文件存在: True
2025-08-27 22:20:00.934 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1556 | 🔧 [列宽保存修复] 父目录存在: True
2025-08-27 22:20:00.935 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1557 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-08-27 22:20:00.936 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2301 | 列宽管理器初始化完成
2025-08-27 22:20:00.937 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2428 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-08-27 22:20:00.940 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2315 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-08-27 22:20:00.948 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-27 22:20:00.949 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-27 22:20:00.951 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-27 22:20:00.963 | INFO     | src.gui.prototype.widgets.pagination_state_manager:__init__:78 | 🔧 [P1优化] 分页状态管理器初始化完成
2025-08-27 22:20:00.965 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 0
2025-08-27 22:20:00.966 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2838 | 🔧 [P1优化] 表切换操作: None -> default_table
2025-08-27 22:20:00.967 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-27 22:20:00.979 | INFO     | src.gui.prototype.widgets.table_header_cache:__init__:67 | 🔧 [P1优化] 表头缓存管理器初始化完成，最大缓存: 100
2025-08-27 22:20:00.980 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2890 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-27 22:20:00.981 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-27 22:20:00.997 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-27 22:20:00.997 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-27 22:20:00.999 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-27 22:20:00.999 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-27 22:20:01.000 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-27 22:20:01.006 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-08-27 22:20:01.010 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 55.6ms
2025-08-27 22:20:01.010 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-27 22:20:01.011 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-27 22:20:01.028 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-27 22:20:01.033 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-27 22:20:01.033 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-27 22:20:01.047 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-08-27 22:20:01.065 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-08-27 22:20:01.066 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-08-27 22:20:01.119 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:594 | 控制面板按钮信号连接完成
2025-08-27 22:20:01.179 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-27 22:20:01.179 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-27 22:20:01.185 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5420 | 快捷键设置完成
2025-08-27 22:20:01.186 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5377 | 主窗口UI设置完成。
2025-08-27 22:20:01.187 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5614 | 🔧 [全局排序] 全局排序开关连接成功
2025-08-27 22:20:01.188 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5646 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-08-27 22:20:01.189 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5658 | ✅ 已连接分页刷新信号到主窗口
2025-08-27 22:20:01.191 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5659 | ✅ 已连接分页组件事件到新架构
2025-08-27 22:20:01.200 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5670 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-08-27 22:20:01.201 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5673 | 信号连接设置完成
2025-08-27 22:20:01.202 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6849 | 🔧 [P1-2修复] 发现 2 个表的配置
2025-08-27 22:20:01.203 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: salary_data_2025_07_active_employees, 10个字段
2025-08-27 22:20:01.204 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: active_employees, 5个字段
2025-08-27 22:20:01.204 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6859 | ✅ [P1-2修复] 已加载字段映射信息，共2个表的映射
2025-08-27 22:20:01.215 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-27 22:20:01.216 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-27 22:20:01.216 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-27 22:20:01.217 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-27 22:20:01.218 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-27 22:20:01.219 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 22
2025-08-27 22:20:01.220 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-27 22:20:01.221 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-27 22:20:01.231 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-27 22:20:01.231 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-27 22:20:01.232 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 15.6ms
2025-08-27 22:20:01.233 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-27 22:20:01.235 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-27 22:20:01.241 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-27 22:20:01.242 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-27 22:20:01.244 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-27 22:20:01.246 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-27 22:20:01.247 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8551 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-08-27 22:20:01.248 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-27 22:20:01.249 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-27 22:20:01.258 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-27 22:20:01.294 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-27 22:20:01.296 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-27 22:20:01.298 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-27 22:20:01.298 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-27 22:20:01.299 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-27 22:20:01.300 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-27 22:20:01.302 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 7.8ms
2025-08-27 22:20:01.302 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-27 22:20:01.303 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-27 22:20:01.311 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-27 22:20:01.313 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-27 22:20:01.314 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-27 22:20:01.315 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8569 | 已显示标准空表格，表头数量: 22
2025-08-27 22:20:01.316 | INFO     | src.gui.prototype.prototype_main_window:__init__:3704 | 原型主窗口初始化完成
2025-08-27 22:20:01.604 | INFO     | __main__:main:514 | 应用程序启动成功
2025-08-27 22:20:01.611 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-27 22:20:01.612 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-27 22:20:01.613 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-27 22:20:01.613 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1474 | 执行延迟的自动选择最新数据...
2025-08-27 22:20:01.614 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 22:20:01.616 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1497 | 延迟自动选择最新数据失败，可能没有可用数据
2025-08-27 22:20:01.617 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-08-27 22:20:01.618 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2079 | MainWorkspaceArea 响应式适配: sm
2025-08-27 22:20:01.731 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 22:20:02.133 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9453 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet']
2025-08-27 22:20:02.134 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9363 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-27 22:20:02.138 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9377 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet']
2025-08-27 22:20:02.138 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:9911 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet']
2025-08-27 22:20:02.154 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9383 | 🔧 [P0-1] 智能显示亮度修复完成
2025-08-27 22:20:02.617 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-27 22:20:02.618 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1612 | 🔧 [P1-2修复] 3次重试已用尽，可能数据导入尚未完成
2025-08-27 22:20:33.728 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:654 | 数据导入功能被触发，发出 import_requested 信号。
2025-08-27 22:20:33.728 | INFO     | src.gui.prototype.prototype_main_window:_get_suggested_target_path:8374 | 检测到当前在工资表TAB，生成工资表默认路径
2025-08-27 22:20:33.731 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5906 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 08月 > 全部在职人员。打开导入对话框。
2025-08-27 22:20:33.732 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-08-27 22:20:33.735 | INFO     | src.modules.data_import.change_data_config_manager:__init__:43 | 配置管理器初始化完成，配置目录: C:\test\salary_changes\salary_changes\state\change_data_configs
2025-08-27 22:20:33.736 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:78 | 多Sheet导入器初始化完成
2025-08-27 22:20:33.738 | INFO     | src.gui.widgets.target_selection_widget:_load_available_options:250 | 成功加载导航配置
2025-08-27 22:20:33.751 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-27 22:20:33.798 | INFO     | src.gui.main_dialogs:_get_template_fields:2010 | 🔧 [P3-修复] ConfigSyncManager未初始化，使用默认字段模板
2025-08-27 22:20:33.799 | INFO     | src.gui.main_dialogs:_init_field_mapping:1997 | 🔧 [P3-修复] 未找到字段模板，使用内置默认字段列表
2025-08-27 22:20:33.869 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-08-27 22:20:33.871 | INFO     | src.modules.data_import.import_defaults_manager:get_smart_defaults_for_category:244 | 检测到工资表导入，设置table_template为salary_data
2025-08-27 22:20:33.875 | INFO     | src.gui.main_dialogs:_apply_default_settings:2616 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'separate_tables', 'table_template': 'salary_data'}
2025-08-27 22:20:33.876 | INFO     | src.gui.main_dialogs:_setup_tooltips:2989 | 工具提示设置完成
2025-08-27 22:20:33.877 | INFO     | src.gui.main_dialogs:_setup_shortcuts:3028 | 快捷键设置完成
2025-08-27 22:20:33.877 | INFO     | src.gui.main_dialogs:__init__:82 | 数据导入对话框初始化完成。
2025-08-27 22:20:33.878 | INFO     | src.modules.data_import.multi_sheet_importer:set_config_sync_manager:87 | 🔧 [修复] ConfigSyncManager已设置到MultiSheetImporter
2025-08-27 22:20:33.878 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5917 | 🔧 [P0-修复] ConfigSyncManager已设置到数据导入对话框
2025-08-27 22:20:35.928 | INFO     | src.gui.main_dialogs:_on_target_changed:2522 | 目标位置已更新: 异动人员表 > 2025年 > 08月 > 全部在职人员
2025-08-27 22:20:35.929 | INFO     | src.gui.main_dialogs:_on_change_mode_changed:2284 | 🔧 [P2-1修复] 异动表处理模式已切换为: 动态处理（保持原始）
2025-08-27 22:20:35.934 | INFO     | src.gui.main_dialogs:_on_target_changed:2541 | 数据期间自动同步为: 2025-08
2025-08-27 22:20:35.934 | INFO     | src.gui.main_dialogs:_on_target_changed:2561 | 数据描述自动生成: 2025年08月全部在职人员工资
2025-08-27 22:20:37.777 | INFO     | src.gui.main_dialogs:_on_target_changed:2522 | 目标位置已更新: 异动人员表 > 2025年 > 12月 > 全部在职人员
2025-08-27 22:20:37.778 | INFO     | src.gui.main_dialogs:_on_target_changed:2541 | 数据期间自动同步为: 2025-12
2025-08-27 22:20:37.781 | INFO     | src.gui.main_dialogs:_on_target_changed:2561 | 数据描述自动生成: 2025年12月全部在职人员工资
2025-08-27 22:20:44.485 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-27 22:20:45.829 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-08-27 22:20:45.830 | INFO     | src.gui.main_dialogs:_auto_select_sheet_by_category:2651 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-08-27 22:20:49.736 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-27 22:20:49.737 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-27 22:20:49.740 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-27 22:20:49.875 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 23列 (列过滤: 否)
2025-08-27 22:20:49.879 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-27 22:20:49.883 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-08-27 22:20:49.883 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1397行 x 23列
2025-08-27 22:20:49.888 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-27 22:20:49.893 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-08-27 22:20:49.895 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1396行 × 23列
2025-08-27 22:20:49.961 | INFO     | src.modules.data_import.change_data_config_manager:__init__:43 | 配置管理器初始化完成，配置目录: C:\test\salary_changes\salary_changes\state\change_data_configs
2025-08-27 22:20:49.961 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: number
2025-08-27 22:20:49.966 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: string
2025-08-27 22:20:49.967 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: date
2025-08-27 22:20:49.968 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: code
2025-08-27 22:20:49.968 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: custom
2025-08-27 22:20:49.969 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: salary_float - 工资金额
2025-08-27 22:20:49.970 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: employee_id_string - 工号
2025-08-27 22:20:49.971 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: name_string - 姓名
2025-08-27 22:20:49.971 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: date_string - 日期
2025-08-27 22:20:49.972 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: id_number_string - 身份证号
2025-08-27 22:20:49.973 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: code_string - 代码
2025-08-27 22:20:49.973 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: float - 浮点数
2025-08-27 22:20:49.976 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: integer - 整数
2025-08-27 22:20:49.983 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: text_string - 文本字符串
2025-08-27 22:20:49.984 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: personnel_category_code - 人员类别代码
2025-08-27 22:20:49.985 | INFO     | src.modules.data_import.field_type_manager:__init__:41 | 字段类型管理器初始化完成，存储目录: C:\test\salary_changes\salary_changes\state\field_types
2025-08-27 22:20:50.527 | INFO     | src.gui.change_data_config_dialog:load_all_sheets:1612 | 成功加载 4 个工作表
2025-08-27 22:21:00.388 | INFO     | src.gui.change_data_config_dialog:load_selected_config:1127 | 尝试加载系统配置: tt1
2025-08-27 22:21:00.390 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '序号' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:21:00.394 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '工号' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:21:00.395 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '姓名' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:21:00.396 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '部门名称' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:21:00.397 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '人员类别代码' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:21:00.398 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '人员类别' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:21:00.399 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '2025年岗位工资' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:21:00.400 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '2025年薪级工资' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:21:00.419 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '津贴' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:21:00.421 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '结余津贴' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:21:00.423 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '2025年基础性绩效' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:21:00.424 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '卫生费' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:21:00.425 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '交通补贴' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:21:00.426 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '物业补贴' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:21:00.435 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '住房补贴' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:21:00.459 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '车补' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:21:00.466 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '通讯补贴' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:21:00.477 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '2025年奖励性绩效预发' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:21:00.482 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '补发' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:21:00.483 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '借支' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:21:00.484 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '应发工资' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:21:00.485 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '2025公积金' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:21:00.486 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '代扣代存养老保险' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:21:00.491 | INFO     | src.gui.change_data_config_dialog:load_selected_config:1158 | 已加载配置: system:tt1
2025-08-27 22:21:14.220 | INFO     | src.gui.change_data_config_dialog:refresh_preview:711 | 预览已刷新
2025-08-27 22:22:15.377 | INFO     | src.gui.change_data_config_dialog:auto_detect_field_types:407 | 开始智能识别字段类型
2025-08-27 22:22:15.421 | INFO     | src.gui.change_data_config_dialog:auto_detect_field_types:434 | 字段类型识别完成
2025-08-27 22:22:24.425 | INFO     | src.modules.data_import.change_data_config_manager:load_user_config:611 | 成功加载用户配置: tt1
2025-08-27 22:22:24.425 | INFO     | src.gui.change_data_config_dialog:load_selected_config:1122 | 尝试加载用户配置: tt1
2025-08-27 22:22:25.667 | WARNING  | src.gui.change_data_config_dialog:load_selected_config:1162 | 配置加载失败: user:tt1
2025-08-27 22:22:32.388 | INFO     | src.gui.change_data_config_dialog:load_selected_config:1127 | 尝试加载系统配置: tt1
2025-08-27 22:22:32.390 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '序号' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:22:32.393 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '工号' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:22:32.394 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '姓名' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:22:32.396 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '部门名称' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:22:32.397 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '人员类别代码' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:22:32.398 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '人员类别' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:22:32.399 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '2025年岗位工资' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:22:32.400 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '2025年薪级工资' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:22:32.407 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '津贴' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:22:32.408 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '结余津贴' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:22:32.409 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '2025年基础性绩效' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:22:32.410 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '卫生费' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:22:32.412 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '交通补贴' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:22:32.413 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '物业补贴' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:22:32.414 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '住房补贴' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:22:32.422 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '车补' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:22:32.434 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '通讯补贴' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:22:32.435 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '2025年奖励性绩效预发' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:22:32.436 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '补发' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:22:32.438 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '借支' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:22:32.439 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '应发工资' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:22:32.440 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '2025公积金' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:22:32.447 | WARNING  | src.gui.change_data_config_dialog:apply_config_to_ui:1225 | 字段 '代扣代存养老保险' 的类型控件不是QComboBox，而是 <class 'PyQt5.QtWidgets.QWidget'>
2025-08-27 22:22:32.448 | INFO     | src.gui.change_data_config_dialog:load_selected_config:1158 | 已加载配置: system:tt1
2025-08-27 23:07:28.253 | INFO     | __main__:main:519 | 应用程序正常退出
2025-08-28 10:25:35.720 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-08-28 10:25:35.723 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-08-28 10:25:35.723 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-08-28 10:25:35.723 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-08-28 10:25:35.724 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-08-28 10:25:35.724 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-28 10:25:37.906 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 3 个工作表选项
2025-08-28 10:25:37.947 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 测试配置, 工作表数: 3
2025-08-28 10:25:38.395 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 3 个工作表选项
2025-08-28 10:25:38.396 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 测试配置, 工作表数: 3
2025-08-28 10:25:38.397 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 3 个工作表选项
2025-08-28 10:25:38.397 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 测试配置, 工作表数: 3
2025-08-28 10:25:38.415 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 3 个工作表选项
2025-08-28 10:25:38.416 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 测试配置, 工作表数: 3
2025-08-28 10:25:38.501 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 2 个工作表选项
2025-08-28 10:25:38.502 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 集成测试, 工作表数: 2
2025-08-28 10:27:10.253 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-08-28 10:27:10.253 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-08-28 10:27:10.254 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-08-28 10:27:10.254 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-08-28 10:27:10.254 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-08-28 10:27:10.255 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-28 10:27:11.277 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 3 个工作表选项
2025-08-28 10:27:11.278 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 测试配置, 工作表数: 3
2025-08-28 10:27:11.281 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 3 个工作表选项
2025-08-28 10:27:11.282 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 测试配置, 工作表数: 3
2025-08-28 10:27:11.286 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 3 个工作表选项
2025-08-28 10:27:11.288 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 测试配置, 工作表数: 3
2025-08-28 10:27:11.293 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 3 个工作表选项
2025-08-28 10:27:11.293 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 测试配置, 工作表数: 3
2025-08-28 10:27:11.300 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 2 个工作表选项
2025-08-28 10:27:11.301 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 集成测试, 工作表数: 2
2025-08-28 10:30:01.019 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-08-28 10:30:01.020 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-08-28 10:30:01.020 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-08-28 10:30:01.021 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-08-28 10:30:01.022 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-08-28 10:30:01.022 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-28 10:30:02.155 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 3 个工作表选项
2025-08-28 10:30:02.156 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 测试配置, 工作表数: 3
2025-08-28 10:30:02.160 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 3 个工作表选项
2025-08-28 10:30:02.160 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 测试配置, 工作表数: 3
2025-08-28 10:30:02.166 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 3 个工作表选项
2025-08-28 10:30:02.166 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 测试配置, 工作表数: 3
2025-08-28 10:30:02.205 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 3 个工作表选项
2025-08-28 10:30:02.205 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 测试配置, 工作表数: 3
2025-08-28 10:30:02.216 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 2 个工作表选项
2025-08-28 10:30:02.216 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 集成测试, 工作表数: 2
2025-08-28 10:34:53.015 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-08-28 10:34:53.015 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-08-28 10:34:53.015 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-08-28 10:34:53.015 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-08-28 10:34:53.015 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-08-28 10:34:53.030 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-28 10:34:54.204 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 3 个工作表选项
2025-08-28 10:34:54.205 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 测试配置, 工作表数: 3
2025-08-28 10:34:54.211 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 3 个工作表选项
2025-08-28 10:34:54.212 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 测试配置, 工作表数: 3
2025-08-28 10:34:54.219 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 3 个工作表选项
2025-08-28 10:34:54.220 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 测试配置, 工作表数: 3
2025-08-28 10:34:54.226 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 3 个工作表选项
2025-08-28 10:34:54.229 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 测试配置, 工作表数: 3
2025-08-28 10:34:54.280 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 2 个工作表选项
2025-08-28 10:34:54.282 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 集成测试, 工作表数: 2
2025-08-28 10:43:34.156 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-08-28 10:43:34.156 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-08-28 10:43:34.157 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-08-28 10:43:34.157 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-08-28 10:43:34.157 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-08-28 10:43:34.158 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-28 10:43:35.434 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 3 个工作表选项
2025-08-28 10:43:35.435 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 测试配置, 工作表数: 3
2025-08-28 10:43:35.438 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 3 个工作表选项
2025-08-28 10:43:35.439 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 测试配置, 工作表数: 3
2025-08-28 10:43:35.443 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 3 个工作表选项
2025-08-28 10:43:35.444 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 测试配置, 工作表数: 3
2025-08-28 10:43:35.451 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 3 个工作表选项
2025-08-28 10:43:35.453 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 测试配置, 工作表数: 3
2025-08-28 10:43:35.459 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 2 个工作表选项
2025-08-28 10:43:35.460 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 集成测试, 工作表数: 2
2025-08-28 10:56:37.944 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-08-28 10:56:37.945 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-08-28 10:56:37.945 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-08-28 10:56:37.946 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-08-28 10:56:37.946 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-08-28 10:56:37.947 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-28 10:56:38.972 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 3 个工作表选项
2025-08-28 10:56:38.972 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 测试配置, 工作表数: 3
2025-08-28 10:56:38.977 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 3 个工作表选项
2025-08-28 10:56:38.977 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 测试配置, 工作表数: 3
2025-08-28 10:56:38.981 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 3 个工作表选项
2025-08-28 10:56:38.981 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 测试配置, 工作表数: 3
2025-08-28 10:56:38.985 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 3 个工作表选项
2025-08-28 10:56:38.986 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 测试配置, 工作表数: 3
2025-08-28 10:56:38.990 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:_load_sheet_data:171 | 已加载 2 个工作表选项
2025-08-28 10:56:38.992 | INFO     | src.gui.widgets.multi_sheet_selection_dialog:__init__:48 | 多工作表选择对话框初始化完成，配置: 集成测试, 工作表数: 2
