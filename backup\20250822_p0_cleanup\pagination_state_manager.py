#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分页状态管理器
🔧 [立即修复] 强化分页防循环机制，使用原子操作和时间戳管理
"""

import time
import threading
from typing import Dict, Optional, Any
from dataclasses import dataclass
from enum import Enum

from src.utils.log_config import setup_logger


class PaginationState(Enum):
    """分页状态枚举"""
    IDLE = "idle"           # 空闲状态
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"    # 已完成
    FAILED = "failed"         # 失败


@dataclass
class PaginationRequest:
    """分页请求记录"""
    table_name: str
    page: int
    timestamp: float
    request_id: str
    state: PaginationState
    thread_id: int
    sort_columns: Optional[list] = None
    completion_time: Optional[float] = None  # 🔧 [P1修复] 添加完成时间字段


class PaginationStateManager:
    """
    🔧 [立即修复] 分页状态管理器
    
    使用原子操作和时间戳管理分页处理标志，防止竞态条件
    """
    
    def __init__(self):
        """初始化分页状态管理器"""
        self.logger = setup_logger(self.__class__.__name__)
        
        # 使用可重入锁确保线程安全
        self._lock = threading.RLock()
        
        # 分页请求状态记录
        self._active_requests: Dict[str, PaginationRequest] = {}
        
        # 全局分页处理状态
        self._global_processing = False
        self._global_processing_start_time = 0.0
        
        # 配置参数
        self._max_processing_time = 10.0  # 最大处理时间（秒）
        self._request_timeout = 5.0       # 请求超时时间（秒）
        
        self.logger.info("🔧 [立即修复] 分页状态管理器初始化完成")
    
    def can_start_pagination(self, table_name: str, page: int, 
                           sort_columns: Optional[list] = None) -> bool:
        """
        🔧 [立即修复] 检查是否可以开始分页操作
        
        Args:
            table_name: 表名
            page: 页码
            sort_columns: 排序列
            
        Returns:
            True: 可以开始分页, False: 不能开始（有冲突）
        """
        current_time = time.time()
        thread_id = threading.get_ident()
        request_key = self._generate_request_key(table_name, page, sort_columns)
        
        with self._lock:
            # 检查全局处理状态
            if self._global_processing:
                processing_duration = current_time - self._global_processing_start_time
                if processing_duration > self._max_processing_time:
                    # 处理时间过长，强制重置
                    self.logger.warning(f"🔧 [强制重置] 分页处理超时，强制重置: {processing_duration:.2f}s")
                    self._global_processing = False
                    self._global_processing_start_time = 0.0
                    self._active_requests.clear()
                else:
                    self.logger.info(f"🚫 [防循环] 全局分页正在处理中，拒绝新请求: {table_name}, 页{page}")
                    return False
            
            # 检查是否有相同的活跃请求
            if request_key in self._active_requests:
                existing_request = self._active_requests[request_key]
                request_age = current_time - existing_request.timestamp
                
                if request_age < self._request_timeout:
                    if existing_request.state == PaginationState.PROCESSING:
                        self.logger.info(f"🚫 [防循环] 相同分页请求正在处理: {table_name}, 页{page}")
                        return False
                else:
                    # 请求超时，清理
                    self.logger.warning(f"🔧 [超时清理] 清理超时分页请求: {table_name}, 页{page}")
                    del self._active_requests[request_key]
            
            # 清理过期请求
            self._cleanup_expired_requests(current_time)
            
            self.logger.info(f"✅ [允许] 分页请求可以开始: {table_name}, 页{page}")
            return True
    
    def start_pagination(self, table_name: str, page: int, 
                        sort_columns: Optional[list] = None) -> str:
        """
        🔧 [立即修复] 开始分页操作
        
        Args:
            table_name: 表名
            page: 页码
            sort_columns: 排序列
            
        Returns:
            请求ID
        """
        current_time = time.time()
        thread_id = threading.get_ident()
        request_key = self._generate_request_key(table_name, page, sort_columns)
        request_id = f"page_{int(current_time * 1000000)}_{thread_id}"
        
        with self._lock:
            # 创建分页请求记录
            request = PaginationRequest(
                table_name=table_name,
                page=page,
                timestamp=current_time,
                request_id=request_id,
                state=PaginationState.PROCESSING,
                thread_id=thread_id,
                sort_columns=sort_columns
            )
            
            self._active_requests[request_key] = request
            
            # 设置全局处理状态
            self._global_processing = True
            self._global_processing_start_time = current_time
            
            self.logger.info(f"🔧 [开始] 分页操作已开始: {table_name}, 页{page}, ID={request_id}")
            return request_id
    
    def complete_pagination(self, table_name: str, page: int, 
                          sort_columns: Optional[list] = None, 
                          success: bool = True) -> bool:
        """
        🔧 [P1修复] 完成分页操作 - 改进状态清理逻辑
        
        Args:
            table_name: 表名
            page: 页码
            sort_columns: 排序列
            success: 是否成功
            
        Returns:
            True: 成功完成, False: 未找到对应请求
        """
        request_key = self._generate_request_key(table_name, page, sort_columns)
        
        with self._lock:
            if request_key in self._active_requests:
                request = self._active_requests[request_key]
                request.state = PaginationState.COMPLETED if success else PaginationState.FAILED
                
                # 记录完成时间
                request.completion_time = time.time()
                duration = request.completion_time - request.timestamp
                
                # 清理请求记录
                del self._active_requests[request_key]
                
                # 如果没有其他活跃请求，清除全局处理状态
                if not self._active_requests:
                    self._global_processing = False
                    self._global_processing_start_time = 0.0
                
                self.logger.info(f"🔧 [P1修复] 分页操作已完成: {table_name}, 页{page}, 成功={success}, 耗时={duration:.3f}s")
                return True
            else:
                # 🔧 [P1修复] 改进未找到请求的处理逻辑
                # 检查是否是超时自动清理导致的
                current_time = time.time()
                if self._global_processing and (current_time - self._global_processing_start_time) > 30:
                    # 可能是超时导致的状态不一致，重置全局状态
                    self.logger.warning(f"🔧 [P1修复] 检测到长时间未完成的分页操作，重置全局状态")
                    self.force_reset()
                
                self.logger.debug(f"🔧 [P1修复] 未找到对应的分页请求: {table_name}, 页{page} (可能已被清理或超时)")
                return False
    
    def force_reset(self):
        """🔧 [立即修复] 强制重置所有分页状态"""
        with self._lock:
            active_count = len(self._active_requests)
            self._active_requests.clear()
            self._global_processing = False
            self._global_processing_start_time = 0.0
            
            self.logger.warning(f"🔧 [强制重置] 已清理所有分页状态，清理请求数: {active_count}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取当前状态信息"""
        with self._lock:
            return {
                'global_processing': self._global_processing,
                'active_requests_count': len(self._active_requests),
                'processing_duration': time.time() - self._global_processing_start_time if self._global_processing else 0,
                'active_requests': [
                    {
                        'table_name': req.table_name,
                        'page': req.page,
                        'state': req.state.value,
                        'age': time.time() - req.timestamp
                    }
                    for req in self._active_requests.values()
                ]
            }
    
    def _generate_request_key(self, table_name: str, page: int, 
                            sort_columns: Optional[list] = None) -> str:
        """生成请求键"""
        sort_key = ""
        if sort_columns:
            sort_parts = []
            for col in sort_columns:
                if isinstance(col, dict):
                    col_name = col.get('column_name', '')
                    order = col.get('order', 'asc')
                    sort_parts.append(f"{col_name}:{order}")
            sort_key = "|".join(sort_parts)
        
        return f"{table_name}:p{page}:s{sort_key}"
    
    def _cleanup_expired_requests(self, current_time: float):
        """清理过期请求"""
        expired_keys = []
        
        for key, request in self._active_requests.items():
            age = current_time - request.timestamp
            if age > self._request_timeout:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self._active_requests[key]
        
        if expired_keys:
            self.logger.debug(f"🔧 [清理] 清理过期分页请求: {len(expired_keys)}个")

    def set_table_total_records(self, table_name: str, total_records: int):
        """🔧 [P2-统一状态] 设置表的总记录数"""
        with self._lock:
            if not hasattr(self, '_table_total_records'):
                self._table_total_records = {}

            self._table_total_records[table_name] = {
                'total_records': total_records,
                'timestamp': time.time()
            }

            self.logger.debug(f"🔧 [P2-统一状态] 设置表总记录数: {table_name} = {total_records}")

    def get_table_total_records(self, table_name: str) -> int:
        """🔧 [P2-统一状态] 获取表的总记录数"""
        with self._lock:
            if hasattr(self, '_table_total_records') and table_name in self._table_total_records:
                record_info = self._table_total_records[table_name]
                # 检查记录是否过期（5分钟）
                if time.time() - record_info['timestamp'] < 300:
                    return record_info['total_records']
                else:
                    # 记录过期，删除
                    del self._table_total_records[table_name]
                    self.logger.debug(f"🔧 [P2-统一状态] 表总记录数已过期: {table_name}")

            return 0


# 全局单例实例
_pagination_state_manager = None


def get_pagination_state_manager() -> PaginationStateManager:
    """获取分页状态管理器单例"""
    global _pagination_state_manager
    if _pagination_state_manager is None:
        _pagination_state_manager = PaginationStateManager()
    return _pagination_state_manager
